"""
Document Processor for MCP Resources
Handles PDF processing, content extraction, metadata extraction, and indexing
"""

import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import hashlib

import fitz  # PyMuPDF
import numpy as np
import faiss
import httpx
import asyncio
import json
import re
from collections import defaultdict

from config import config
from cache_manager import create_cache_manager, DocumentCacheManager
from mcp_server import MCPResource
from security_utils import security_validator, error_handler

logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """Represents a chunk of document content"""
    text: str
    page_number: int
    chunk_index: int
    embedding: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = None

@dataclass
class ProcessedDocument:
    """Represents a fully processed document"""
    resource: MCPResource
    chunks: List[DocumentChunk]
    full_text: str
    page_count: int
    tables: List[Dict[str, Any]]
    images: List[Dict[str, Any]]
    
class PDFProcessor:
    """Handles PDF content extraction and processing"""
    
    def __init__(self):
        self.max_file_size = config.max_file_size_mb * 1024 * 1024
        self.chunk_size = config.chunk_size
        self.overlap_size = config.overlap_size
    
    async def extract_content(self, pdf_path: Path, cache_manager: Optional[DocumentCacheManager] = None) -> Tuple[str, List[Dict], List[Dict], int]:
        """Extract text, tables, images, and metadata from PDF with caching support"""
        try:
            # Validate file path
            is_valid, validation_error = security_validator.validate_file_path(pdf_path)
            if not is_valid:
                raise ValueError(f"File validation failed: {validation_error}")

            # Check file size
            if pdf_path.stat().st_size > self.max_file_size:
                raise ValueError(f"File too large: {pdf_path.stat().st_size} bytes")

            # Try to get from cache first
            if cache_manager:
                cached_content = await cache_manager.get_content_cache(pdf_path)
                if cached_content:
                    logger.info(f"Using cached content for: {pdf_path.name}")
                    return cached_content

            # Extract content if not cached
            doc = fitz.open(pdf_path)
            full_text = ""
            tables = []
            images = []
            page_count = len(doc)

            for page_num in range(page_count):
                page = doc[page_num]

                # Extract text
                page_text = page.get_text()
                full_text += f"\n--- Page {page_num + 1} ---\n{page_text}"

                # Extract tables (basic implementation)
                tables.extend(self._extract_tables_from_page(page, page_num + 1))

                # Extract images with descriptions
                images.extend(self._extract_images_from_page(page, page_num + 1))

            doc.close()

            # Clean up text
            full_text = self._clean_text(full_text)

            content_data = (full_text, tables, images, page_count)

            # Cache the extracted content
            if cache_manager:
                await cache_manager.set_content_cache(pdf_path, content_data)

            logger.info(f"Extracted content from {pdf_path}: {len(full_text)} chars, {len(tables)} tables, {len(images)} images")

            return content_data

        except Exception as e:
            logger.error(f"Error extracting content from {pdf_path}: {e}")
            raise
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[Dict]:
        """Extract tables and technical specifications from a page"""
        tables = []
        try:
            text = page.get_text()
            lines = text.split('\n')

            # Enhanced table detection
            table_lines = []
            technical_specs = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Detect technical specifications (measurements, limits, etc.)
                if any(keyword in line.lower() for keyword in [
                    'limite:', 'limit:', 'diametro', 'misurare', 'sostituire',
                    'mm', 'kg', 'nm', 'rpm', 'volt', 'ohm', 'amp',
                    'specifiche', 'parametri', 'tolleranza', 'gioco'
                ]):
                    technical_specs.append(line)

                # Detect table-like structures
                if (len(line.split()) > 2 and
                    ('\t' in line or '  ' in line or ':' in line or
                     any(char in line for char in ['|', '─', '┌', '└', '├']))):
                    table_lines.append(line)

            # Create table entries
            if table_lines or technical_specs:
                # Combine table lines and technical specs
                all_content = []
                if table_lines:
                    all_content.extend(table_lines)
                if technical_specs:
                    all_content.extend(technical_specs)

                if all_content:
                    tables.append({
                        'page': page_num,
                        'content': '\n'.join(all_content),
                        'type': 'enhanced_table',
                        'has_technical_specs': len(technical_specs) > 0,
                        'has_table_structure': len(table_lines) > 0
                    })

        except Exception as e:
            logger.warning(f"Error extracting tables from page {page_num}: {e}")

        return tables
    
    def _extract_images_from_page(self, page, page_num: int) -> List[Dict]:
        """Extract images and generate descriptions"""
        images = []
        try:
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                # Get image info
                xref = img[0]
                
                # Generate basic description based on context
                # In a real implementation, you might use OCR or image analysis
                description = f"Image {img_index + 1} on page {page_num}"
                
                images.append({
                    'page': page_num,
                    'index': img_index,
                    'description': description,
                    'xref': xref
                })
                
        except Exception as e:
            logger.warning(f"Error extracting images from page {page_num}: {e}")
        
        return images
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove excessive whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)
        
        # Remove page headers/footers (basic heuristic)
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # Skip very short lines that might be headers/footers
            if len(line) > 3 or line.startswith('---'):
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    async def create_chunks(self, text: str, tables: List[Dict] = None, file_path: Optional[Path] = None, cache_manager: Optional[DocumentCacheManager] = None) -> List[DocumentChunk]:
        """Create intelligent chunks preserving technical content and tables with caching support"""

        # Try to get from cache first
        if cache_manager and file_path:
            cached_chunks = await cache_manager.get_chunks_cache(file_path)
            if cached_chunks:
                logger.info(f"Using cached chunks for: {file_path.name}")
                return cached_chunks

        chunks = []

        if not text:
            return chunks

        # Split text by pages first
        page_sections = text.split('--- Page ')

        chunk_index = 0

        for i, section in enumerate(page_sections):
            if not section.strip():
                continue

            # Extract page number
            if i == 0:
                page_number = 1
                page_text = section
            else:
                lines = section.split('\n', 1)
                try:
                    page_number = int(lines[0].split(' ---')[0])
                    page_text = lines[1] if len(lines) > 1 else ""
                except (ValueError, IndexError):
                    page_number = i + 1
                    page_text = section

            # Find tables for this page
            page_tables = [t for t in (tables or []) if t.get('page') == page_number]

            # Create chunks for this page
            page_chunks = self._create_page_chunks(page_text, page_number, page_tables, chunk_index)
            chunks.extend(page_chunks)
            chunk_index += len(page_chunks)

        # Cache the chunks
        if cache_manager and file_path:
            await cache_manager.set_chunks_cache(file_path, chunks)

        logger.info(f"Created {len(chunks)} intelligent chunks from text")
        return chunks

    def _create_page_chunks(self, page_text: str, page_number: int, page_tables: List[Dict], start_chunk_index: int) -> List[DocumentChunk]:
        """Create chunks for a single page, preserving table context"""
        chunks = []

        # If page has tables, create specialized chunks
        if page_tables:
            # Create a comprehensive chunk that includes both text and tables
            combined_content = page_text

            # Add table content to ensure it's searchable
            for table in page_tables:
                table_content = f"\n\n[TABELLA PAGINA {page_number}]\n{table.get('content', '')}"
                combined_content += table_content

            # Create chunk with combined content
            chunk = DocumentChunk(
                text=combined_content,
                page_number=page_number,
                chunk_index=start_chunk_index,
                metadata={
                    'has_tables': True,
                    'table_count': len(page_tables),
                    'content_type': 'page_with_tables'
                }
            )
            chunks.append(chunk)

            # Also create separate table chunks for better searchability
            for i, table in enumerate(page_tables):
                table_chunk = DocumentChunk(
                    text=f"Pagina {page_number} - Tabella {i+1}:\n{table.get('content', '')}",
                    page_number=page_number,
                    chunk_index=start_chunk_index + 1 + i,
                    metadata={
                        'content_type': 'table_only',
                        'table_index': i
                    }
                )
                chunks.append(table_chunk)

        else:
            # For pages without tables, use traditional chunking but preserve page boundaries
            words = page_text.split()
            if words:
                # Create smaller chunks for pages without tables
                chunk_size = min(self.chunk_size, len(words))

                chunk = DocumentChunk(
                    text=page_text,
                    page_number=page_number,
                    chunk_index=start_chunk_index,
                    metadata={
                        'has_tables': False,
                        'content_type': 'text_only',
                        'word_count': len(words)
                    }
                )
                chunks.append(chunk)

        return chunks
    
    def _estimate_page_number(self, chunk_text: str, word_position: int, total_words: int) -> int:
        """Estimate page number for a chunk (simplified)"""
        # Look for page markers in text
        page_match = re.search(r'--- Page (\d+) ---', chunk_text)
        if page_match:
            return int(page_match.group(1))
        
        # Fallback: estimate based on position
        estimated_page = max(1, int((word_position / total_words) * 10))  # Assume ~10 pages average
        return estimated_page

class EmbeddingManager:
    """Manages document embeddings using Jina Embeddings v4 API"""

    def __init__(self):
        self.index = None
        self.chunk_mapping = {}  # Maps index positions to chunks
        self.api_key = config.jina_api_key
        self.api_url = config.jina_api_url
        self.client = httpx.AsyncClient(timeout=30.0)
        logger.info("Initialized Jina Embeddings v4 API client")

    def _truncate_text_to_token_limit(self, text: str, max_tokens: int = 8000) -> str:
        """Truncate text to stay within token limits (rough estimation)"""
        # Rough estimation: 1 token ≈ 4 characters for mixed language text
        max_chars = max_tokens * 4
        if len(text) <= max_chars:
            return text

        # Truncate and try to end at a sentence boundary
        truncated = text[:max_chars]
        last_period = truncated.rfind('.')
        last_newline = truncated.rfind('\n')

        # Use the latest sentence/line boundary
        boundary = max(last_period, last_newline)
        if boundary > max_chars * 0.8:  # If boundary is reasonably close to limit
            return truncated[:boundary + 1]
        else:
            return truncated

    async def _get_embeddings_from_jina(self, texts: List[str]) -> np.ndarray:
        """Get embeddings from Jina API with error handling and text truncation"""
        try:
            # Truncate texts that are too long
            processed_texts = []
            for text in texts:
                truncated_text = self._truncate_text_to_token_limit(text)
                processed_texts.append(truncated_text)
                if len(text) != len(truncated_text):
                    logger.warning(f"Truncated text from {len(text)} to {len(truncated_text)} characters")

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "model": "jina-embeddings-v3",  # Fallback to v3 for stability
                "task": "retrieval.passage",  # Optimized for document retrieval
                "dimensions": 1024,  # High-dimensional embeddings for better accuracy
                "input": processed_texts
            }

            response = await self.client.post(
                self.api_url,
                headers=headers,
                json=payload
            )

            if response.status_code != 200:
                logger.error(f"Jina API error: {response.status_code} - {response.text}")
                # Try with v2 as fallback
                if "jina-embeddings-v3" in str(payload.get("model")):
                    logger.info("Trying fallback to jina-embeddings-v2-base-en")
                    payload["model"] = "jina-embeddings-v2-base-en"
                    payload["dimensions"] = 768  # v2 uses 768 dimensions

                    response = await self.client.post(
                        self.api_url,
                        headers=headers,
                        json=payload
                    )

                if response.status_code != 200:
                    raise Exception(f"Jina API request failed: {response.status_code}")

            result = response.json()
            embeddings = []

            for item in result.get("data", []):
                embeddings.append(item["embedding"])

            if not embeddings:
                raise Exception("No embeddings returned from Jina API")

            return np.array(embeddings, dtype=np.float32)

        except Exception as e:
            logger.error(f"Error getting embeddings from Jina: {e}")
            raise
    
    async def create_embeddings(self, chunks: List[DocumentChunk], cache_manager: Optional[DocumentCacheManager] = None) -> List[DocumentChunk]:
        """Create embeddings for document chunks using Jina API with robust error handling and caching"""
        try:
            if not chunks:
                return chunks

            # Check cache for existing embeddings
            cached_chunks = []
            uncached_chunks = []

            if cache_manager:
                for chunk in chunks:
                    text_hash = cache_manager.generate_text_hash(chunk.text)
                    cached_embedding = await cache_manager.get_embeddings_cache(text_hash)

                    if cached_embedding is not None:
                        chunk.embedding = cached_embedding
                        cached_chunks.append(chunk)
                    else:
                        uncached_chunks.append(chunk)

                if cached_chunks:
                    logger.info(f"Using cached embeddings for {len(cached_chunks)} chunks")
            else:
                uncached_chunks = chunks

            # Process uncached chunks
            if uncached_chunks:
                # Extract texts
                texts = [chunk.text for chunk in uncached_chunks]

                # Process in smaller batches to avoid API limits and timeouts
                batch_size = 20  # Reduced batch size for better reliability
                all_embeddings = []
                successful_chunks = []

                for i in range(0, len(texts), batch_size):
                    batch_texts = texts[i:i + batch_size]
                    batch_chunks = uncached_chunks[i:i + batch_size]

                    try:
                        batch_embeddings = await self._get_embeddings_from_jina(batch_texts)
                        all_embeddings.extend(batch_embeddings)
                        successful_chunks.extend(batch_chunks)

                        logger.info(f"Successfully processed batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")

                    except Exception as batch_error:
                        logger.error(f"Error processing batch {i//batch_size + 1}: {batch_error}")

                        # Try processing texts individually in this batch
                        for j, (text, chunk) in enumerate(zip(batch_texts, batch_chunks)):
                            try:
                                single_embedding = await self._get_embeddings_from_jina([text])
                                all_embeddings.extend(single_embedding)
                                successful_chunks.append(chunk)

                            except Exception as single_error:
                                logger.warning(f"Failed to create embedding for chunk {i+j}: {single_error}")
                                # Skip this chunk - it won't have an embedding
                                continue

                    # Delay between batches to respect rate limits
                    if i + batch_size < len(texts):
                        await asyncio.sleep(0.2)

                # Assign embeddings to successful chunks and cache them
                for chunk, embedding in zip(successful_chunks, all_embeddings):
                    chunk.embedding = embedding

                    # Cache the embedding
                    if cache_manager:
                        text_hash = cache_manager.generate_text_hash(chunk.text)
                        await cache_manager.set_embeddings_cache(text_hash, embedding)

                logger.info(f"Created embeddings for {len(successful_chunks)}/{len(uncached_chunks)} uncached chunks using Jina API")

                # Combine cached and newly created chunks
                all_successful_chunks = cached_chunks + successful_chunks
            else:
                all_successful_chunks = cached_chunks

            logger.info(f"Total chunks with embeddings: {len(all_successful_chunks)}")
            return all_successful_chunks

        except Exception as e:
            logger.error(f"Error creating embeddings: {e}")
            # Return chunks without embeddings - they'll be filtered out later
            return []
    
    def build_index(self, chunks: List[DocumentChunk]) -> bool:
        """Build FAISS index for semantic search with robust error handling"""
        try:
            if not chunks:
                logger.warning("No chunks provided for indexing")
                return False

            # Filter chunks with valid embeddings
            valid_chunks = []
            embeddings_list = []

            for chunk in chunks:
                if (hasattr(chunk, 'embedding') and
                    chunk.embedding is not None and
                    isinstance(chunk.embedding, np.ndarray) and
                    chunk.embedding.size > 0):

                    try:
                        # Ensure embedding is 1D and has consistent shape
                        embedding = chunk.embedding.flatten()
                        if embedding.size > 0:
                            embeddings_list.append(embedding)
                            valid_chunks.append(chunk)
                    except Exception as e:
                        logger.warning(f"Error processing embedding for chunk: {e}")
                        continue

            if not valid_chunks:
                logger.warning("No chunks with valid embeddings found")
                return False

            # Check if all embeddings have the same dimension
            dimensions = [emb.size for emb in embeddings_list]
            if len(set(dimensions)) > 1:
                logger.error(f"Inconsistent embedding dimensions: {set(dimensions)}")
                return False

            # Create embeddings matrix
            embeddings = np.vstack(embeddings_list).astype(np.float32)
            dimension = embeddings.shape[1]

            # Create FAISS index
            self.index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity

            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)

            # Add to index
            self.index.add(embeddings)

            # Update chunk mapping
            self.chunk_mapping = {i: chunk for i, chunk in enumerate(valid_chunks)}

            logger.info(f"Built FAISS index with {len(valid_chunks)} chunks (dimension: {dimension})")
            return True

        except Exception as e:
            logger.error(f"Error building index: {e}")
            self.index = None
            self.chunk_mapping = {}
            return False
    
    async def search(self, query: str, k: int = 5) -> List[Tuple[DocumentChunk, float]]:
        """Search for similar chunks using Jina embeddings"""
        try:
            if not self.index:
                return []

            # Generate query embedding using Jina API
            query_embedding = await self._get_embeddings_from_jina([query])
            faiss.normalize_L2(query_embedding)

            # Search
            scores, indices = self.index.search(query_embedding, k)

            # Prepare results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx in self.chunk_mapping:
                    chunk = self.chunk_mapping[idx]
                    results.append((chunk, float(score)))

            return results

        except Exception as e:
            logger.error(f"Error searching index: {e}")
            return []

class HybridSearchManager:
    """Combines semantic search with keyword-based search for better technical content retrieval"""

    def __init__(self, embedding_manager: EmbeddingManager):
        self.embedding_manager = embedding_manager
        self.technical_terms = {
            'bilanciere', 'diametro', 'interno', 'esterno', 'limite', 'misurare', 'sostituire',
            'parametri', 'specifiche', 'tabella', 'albero', 'valvola', 'pistone', 'cilindro',
            'motore', 'camme', 'aspirazione', 'scarico', 'mm', 'kg', 'nm', 'rpm', 'volt', 'ohm'
        }

    async def hybrid_search(self, query: str, chunks: List[DocumentChunk], k: int = 10) -> List[Tuple[DocumentChunk, float]]:
        """Perform hybrid search combining semantic and keyword matching with fallback"""
        try:
            # Get semantic search results
            semantic_results = await self.embedding_manager.search(query, k=k*2)  # Get more results for reranking

            # Get keyword search results
            keyword_results = self._keyword_search(query, chunks, k=k*2)

            # Combine and rerank results
            combined_results = self._combine_and_rerank(query, semantic_results, keyword_results, k)

            # Check if results are good enough
            if not combined_results or (combined_results and combined_results[0][1] < 0.3):
                logger.info(f"Low confidence results, using fallback text search for: {query}")
                fallback_results = self.fallback_text_search(query, chunks, k)

                # If fallback found better results, use them
                if fallback_results and (not combined_results or fallback_results[0][1] > combined_results[0][1]):
                    return fallback_results

            return combined_results

        except Exception as e:
            logger.error(f"Error in hybrid search, using fallback: {e}")
            return self.fallback_text_search(query, chunks, k)

    def _keyword_search(self, query: str, chunks: List[DocumentChunk], k: int = 10) -> List[Tuple[DocumentChunk, float]]:
        """Perform keyword-based search with technical term boosting"""
        query_lower = query.lower()
        query_words = set(re.findall(r'\b\w+\b', query_lower))

        results = []

        for chunk in chunks:
            content_lower = chunk.text.lower()
            content_words = set(re.findall(r'\b\w+\b', content_lower))

            # Calculate base keyword score
            word_overlap = query_words.intersection(content_words)
            base_score = len(word_overlap) / len(query_words) if query_words else 0

            # Boost for exact phrase matches
            phrase_boost = 0
            if query_lower in content_lower:
                phrase_boost = 0.5

            # Boost for technical terms
            technical_boost = 0
            technical_matches = word_overlap.intersection(self.technical_terms)
            if technical_matches:
                technical_boost = len(technical_matches) * 0.2

            # Boost for measurements (numbers + units)
            measurement_boost = 0
            if re.search(r'\d+\.?\d*\s*(mm|kg|nm|rpm|volt|ohm|amp)', query_lower) and \
               re.search(r'\d+\.?\d*\s*(mm|kg|nm|rpm|volt|ohm|amp)', content_lower):
                measurement_boost = 0.3

            # Calculate final score
            final_score = base_score + phrase_boost + technical_boost + measurement_boost

            if final_score > 0:
                results.append((chunk, final_score))

        # Sort by score and return top k
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:k]

    def _combine_and_rerank(self, query: str, semantic_results: List[Tuple[DocumentChunk, float]],
                           keyword_results: List[Tuple[DocumentChunk, float]], k: int) -> List[Tuple[DocumentChunk, float]]:
        """Combine semantic and keyword results with intelligent reranking"""

        # Create a mapping of chunks to their scores
        chunk_scores = defaultdict(lambda: {'semantic': 0, 'keyword': 0})

        # Add semantic scores
        for chunk, score in semantic_results:
            chunk_scores[id(chunk)]['semantic'] = score
            chunk_scores[id(chunk)]['chunk'] = chunk

        # Add keyword scores
        for chunk, score in keyword_results:
            chunk_scores[id(chunk)]['keyword'] = score
            chunk_scores[id(chunk)]['chunk'] = chunk

        # Calculate combined scores
        final_results = []
        for chunk_id, scores in chunk_scores.items():
            # Weighted combination: semantic (60%) + keyword (40%)
            # But boost keyword score if it's significantly higher
            semantic_score = scores['semantic']
            keyword_score = scores['keyword']

            if keyword_score > semantic_score * 1.5:  # Keyword much better
                combined_score = keyword_score * 0.7 + semantic_score * 0.3
            else:  # Normal weighting
                combined_score = semantic_score * 0.6 + keyword_score * 0.4

            final_results.append((scores['chunk'], combined_score))

        # Sort by combined score and return top k
        final_results.sort(key=lambda x: x[1], reverse=True)
        return final_results[:k]

    def fallback_text_search(self, query: str, chunks: List[DocumentChunk], k: int = 5) -> List[Tuple[DocumentChunk, float]]:
        """Fallback text-based search when semantic search fails"""
        query_lower = query.lower()
        results = []

        for chunk in chunks:
            content_lower = chunk.text.lower()

            # Simple text matching with multiple strategies
            score = 0

            # Exact phrase match (highest priority)
            if query_lower in content_lower:
                score += 1.0

            # Word-by-word matching
            query_words = query_lower.split()
            content_words = content_lower.split()

            word_matches = sum(1 for word in query_words if word in content_words)
            if query_words:
                score += (word_matches / len(query_words)) * 0.8

            # Technical term bonus
            technical_words = [word for word in query_words if word in self.technical_terms]
            technical_matches = sum(1 for word in technical_words if word in content_words)
            if technical_words:
                score += (technical_matches / len(technical_words)) * 0.5

            # Measurement pattern matching
            measurement_pattern = r'\d+\.?\d*\s*(mm|kg|nm|rpm|volt|ohm|amp)'
            query_measurements = re.findall(measurement_pattern, query_lower)
            content_measurements = re.findall(measurement_pattern, content_lower)

            if query_measurements and content_measurements:
                # Check for exact measurement matches
                for q_measure in query_measurements:
                    if q_measure in content_measurements:
                        score += 0.7

            if score > 0:
                results.append((chunk, score))

        # Sort by score and return top k
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:k]

class DocumentProcessor:
    """Main document processor coordinating PDF processing and indexing with advanced caching"""

    def __init__(self, enable_cache: bool = True):
        self.pdf_processor = PDFProcessor()
        self.embedding_manager = EmbeddingManager()
        self.hybrid_search_manager = HybridSearchManager(self.embedding_manager)
        self.processed_docs: Dict[str, ProcessedDocument] = {}
        self._combined_chunks = []
        self._chunk_to_doc_mapping = {}

        # Initialize cache manager
        self.cache_manager = None
        if enable_cache:
            try:
                self.cache_manager = create_cache_manager(
                    cache_type=config.cache_type,
                    cache_dir=config.cache_dir,
                    max_size_mb=config.cache_max_size_mb,
                    ttl_hours=config.cache_ttl_hours,
                    redis_url=getattr(config, 'redis_url', 'redis://localhost:6379'),
                    redis_db=getattr(config, 'redis_db', 0),
                    key_prefix=getattr(config, 'redis_key_prefix', 'docache:')
                )
                logger.info(f"Initialized cache manager: {config.cache_type}")
            except Exception as e:
                logger.warning(f"Failed to initialize cache manager: {e}")
                self.cache_manager = None
    
    async def process_resource(self, resource: MCPResource) -> Optional[ProcessedDocument]:
        """Process a PDF resource into a searchable document with advanced caching"""
        try:
            pdf_path = Path(resource.metadata['file_path'])

            # Check cache for processed document first
            if self.cache_manager:
                cached_doc = await self.cache_manager.get_processed_doc_cache(pdf_path)
                if cached_doc:
                    logger.info(f"Using cached processed document: {resource.name}")
                    # Update in-memory cache as well
                    cache_key = self._get_file_hash(pdf_path)
                    self.processed_docs[cache_key] = cached_doc
                    return cached_doc

            # Check if already processed in memory (fallback)
            cache_key = self._get_file_hash(pdf_path)
            if cache_key in self.processed_docs:
                logger.info(f"Using in-memory cached processed document: {resource.name}")
                return self.processed_docs[cache_key]

            logger.info(f"Processing document: {resource.name}")

            # Extract content with caching
            full_text, tables, images, page_count = await self.pdf_processor.extract_content(
                pdf_path, self.cache_manager
            )

            # Create chunks with caching
            chunks = await self.pdf_processor.create_chunks(
                full_text, tables, pdf_path, self.cache_manager
            )

            # Create embeddings with caching
            chunks = await self.embedding_manager.create_embeddings(chunks, self.cache_manager)

            # Create processed document
            processed_doc = ProcessedDocument(
                resource=resource,
                chunks=chunks,
                full_text=full_text,
                page_count=page_count,
                tables=tables,
                images=images
            )

            # Cache processed document
            if self.cache_manager:
                await self.cache_manager.set_processed_doc_cache(pdf_path, processed_doc)

            # Also cache in memory
            self.processed_docs[cache_key] = processed_doc

            # Update resource with content
            resource.content = full_text[:1000]  # Store preview
            resource.metadata.update({
                'page_count': page_count,
                'chunk_count': len(chunks),
                'table_count': len(tables),
                'image_count': len(images),
                'processed': True
            })

            logger.info(f"Successfully processed document: {resource.name}")
            return processed_doc

        except Exception as e:
            logger.error(f"Error processing resource {resource.name}: {e}")
            return None
    
    def _get_file_hash(self, file_path: Path) -> str:
        """Generate hash for file caching"""
        try:
            # Use file path and modification time for hash
            stat = file_path.stat()
            hash_input = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except Exception:
            return str(file_path)

    async def invalidate_cache(self, file_path: Path):
        """Invalidate cache for a specific file"""
        if self.cache_manager:
            await self.cache_manager.invalidate_file_cache(file_path)

        # Also remove from in-memory cache
        cache_key = self._get_file_hash(file_path)
        if cache_key in self.processed_docs:
            del self.processed_docs[cache_key]
            logger.info(f"Invalidated in-memory cache for: {file_path}")

    async def get_cache_stats(self):
        """Get cache statistics"""
        if self.cache_manager:
            return await self.cache_manager.get_stats()
        return None

    async def cleanup_cache(self):
        """Perform cache cleanup"""
        if self.cache_manager:
            await self.cache_manager.cleanup_expired()
            await self.cache_manager.enforce_size_limit()
            logger.info("Cache cleanup completed")

    async def clear_all_cache(self):
        """Clear all cache entries"""
        if self.cache_manager:
            await self.cache_manager.clear_all()

        # Clear in-memory cache
        self.processed_docs.clear()
        logger.info("All caches cleared")
    
    async def search_documents(self, query: str, product: str = None) -> List[Dict[str, Any]]:
        """Search across processed documents"""
        try:
            all_results = []

            # Build a combined index if we don't have one
            if not hasattr(self, '_combined_chunks') or not self._combined_chunks:
                self._build_combined_index(product)

            # Search using hybrid approach
            if hasattr(self, '_combined_chunks') and self._combined_chunks:
                # Use hybrid search for better technical content retrieval
                chunk_results = await self.hybrid_search_manager.hybrid_search(query, self._combined_chunks, k=10)

                for chunk, score in chunk_results:
                    # Find which document this chunk belongs to
                    doc_info = self._chunk_to_doc_mapping.get(id(chunk))
                    if doc_info and (not product or doc_info['product'] == product):
                        all_results.append({
                            'resource_name': doc_info['resource_name'],
                            'chunk': chunk,
                            'score': score,
                            'page': chunk.page_number,
                            'citation_required': doc_info['citation_required'],
                            'filename': doc_info['filename'],
                            'content': chunk.text
                        })

            # Sort by score
            all_results.sort(key=lambda x: x['score'], reverse=True)

            return all_results[:10]  # Return top 10 results

        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []

    def _build_combined_index(self, product: str = None):
        """Build combined index from all processed documents"""
        try:
            all_chunks = []
            self._chunk_to_doc_mapping = {}

            for processed_doc in self.processed_docs.values():
                # Filter by product if specified
                if product and processed_doc.resource.metadata.get('product') != product:
                    continue

                for chunk in processed_doc.chunks:
                    all_chunks.append(chunk)
                    # Map chunk to document info
                    self._chunk_to_doc_mapping[id(chunk)] = {
                        'resource_name': processed_doc.resource.name,
                        'product': processed_doc.resource.metadata.get('product'),
                        'citation_required': processed_doc.resource.metadata.get('citation_required', False),
                        'filename': processed_doc.resource.metadata.get('filename', '')
                    }

            # Build index with all chunks
            if all_chunks:
                self.embedding_manager.build_index(all_chunks)
                self._combined_chunks = all_chunks
                logger.info(f"Built combined index with {len(all_chunks)} chunks")

        except Exception as e:
            logger.error(f"Error building combined index: {e}")
    
    def get_document_content(self, resource_name: str, page_number: int = None) -> Optional[str]:
        """Get content from a specific document/page"""
        try:
            for processed_doc in self.processed_docs.values():
                if processed_doc.resource.name == resource_name:
                    if page_number:
                        # Return content from specific page
                        page_chunks = [
                            chunk for chunk in processed_doc.chunks 
                            if chunk.page_number == page_number
                        ]
                        return '\n'.join([chunk.text for chunk in page_chunks])
                    else:
                        # Return full document content
                        return processed_doc.full_text
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document content: {e}")
            return None
