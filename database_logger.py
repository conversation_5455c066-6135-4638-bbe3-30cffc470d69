"""
Database Logger for Chatbot Conversations
Handles MySQL connection and logging of all chatbot interactions
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import asyncio
from contextlib import asynccontextmanager

import aiomysql
from config import config

logger = logging.getLogger(__name__)

@dataclass
class ConversationLog:
    """Data structure for conversation logging"""
    session_id: str
    question: str
    answer: Optional[str] = None
    ip_address: Optional[str] = None
    product: Optional[str] = None
    interface_type: str = "CLI"  # "CLI" or "WEB"
    search_result_count: int = 0
    avg_relevance_score: Optional[float] = None
    used_general_knowledge: bool = False
    response_time_ms: Optional[int] = None
    confidence: Optional[float] = None
    tool_executions: Optional[Dict[str, Any]] = None

class DatabaseLogger:
    """Handles database logging for chatbot conversations"""
    
    def __init__(self):
        self.pool: Optional[aiomysql.Pool] = None
        self._connection_config = {
            'host': config.db_host,
            'port': config.db_port,
            'user': config.db_user,
            'password': config.db_password,
            'db': config.db_database,
            'charset': config.db_charset,
            'autocommit': True,
            'connect_timeout': config.db_connection_timeout,
            'maxsize': config.db_pool_size,
            'minsize': 1
        }
    
    async def initialize(self):
        """Initialize database connection pool"""
        try:
            logger.info("Initializing database connection pool...")
            self.pool = await aiomysql.create_pool(**self._connection_config)
            
            # Test connection
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    result = await cursor.fetchone()
                    if result[0] == 1:
                        logger.info("✅ Database connection established successfully")
                    else:
                        raise Exception("Database connection test failed")
                        
        except Exception as e:
            logger.error(f"❌ Failed to initialize database connection: {e}")
            raise
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool"""
        if not self.pool:
            raise Exception("Database pool not initialized")
        
        async with self.pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                await conn.rollback()
                raise e
    
    async def log_conversation(self, log_data: ConversationLog) -> bool:
        """Log a conversation to the database"""
        try:
            async with self.get_connection() as conn:
                async with conn.cursor() as cursor:
                    # Prepare SQL query
                    query = """
                    INSERT INTO chatbot_logs (
                        session_id, question, answer, ip_address, product, interface_type,
                        search_result_count, avg_relevance_score, used_general_knowledge,
                        response_time_ms, confidence, tool_executions
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    """

                    # Prepare values
                    values = (
                        log_data.session_id,
                        log_data.question,
                        log_data.answer,
                        log_data.ip_address,
                        log_data.product,
                        log_data.interface_type,
                        log_data.search_result_count,
                        log_data.avg_relevance_score,
                        log_data.used_general_knowledge,
                        log_data.response_time_ms,
                        log_data.confidence,
                        json.dumps(log_data.tool_executions) if log_data.tool_executions else None
                    )
                    
                    # Execute query
                    await cursor.execute(query, values)
                    
                    logger.debug(f"Logged conversation for session {log_data.session_id}")
                    return True
                    
        except Exception as e:
            logger.error(f"Failed to log conversation: {e}")
            return False
    
    async def get_session_stats(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific session"""
        try:
            async with self.get_connection() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    query = """
                    SELECT 
                        COUNT(*) as total_queries,
                        AVG(response_time_ms) as avg_response_time,
                        AVG(confidence) as avg_confidence,
                        AVG(search_result_count) as avg_search_results,
                        SUM(CASE WHEN used_general_knowledge = 1 THEN 1 ELSE 0 END) as general_knowledge_count,
                        MIN(created_at) as session_start,
                        MAX(created_at) as last_activity
                    FROM chatbot_logs 
                    WHERE session_id = %s
                    """
                    
                    await cursor.execute(query, (session_id,))
                    result = await cursor.fetchone()
                    
                    if result and result['total_queries'] > 0:
                        return {
                            'total_queries': result['total_queries'],
                            'avg_response_time_ms': float(result['avg_response_time']) if result['avg_response_time'] else None,
                            'avg_confidence': float(result['avg_confidence']) if result['avg_confidence'] else None,
                            'avg_search_results': float(result['avg_search_results']) if result['avg_search_results'] else None,
                            'general_knowledge_usage_rate': result['general_knowledge_count'] / result['total_queries'],
                            'session_start': result['session_start'],
                            'last_activity': result['last_activity']
                        }
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get session stats: {e}")
            return None

    async def get_recent_conversations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversations for monitoring"""
        try:
            async with self.get_connection() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    query = """
                    SELECT
                        id, created_at, session_id, product, question, interface_type,
                        response_time_ms, confidence, used_general_knowledge,
                        search_result_count
                    FROM chatbot_logs
                    ORDER BY created_at DESC
                    LIMIT %s
                    """

                    await cursor.execute(query, (limit,))
                    results = await cursor.fetchall()

                    return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Failed to get recent conversations: {e}")
            return []

    async def get_performance_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance metrics for the last N hours"""
        try:
            async with self.get_connection() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    query = """
                    SELECT
                        COUNT(*) as total_conversations,
                        AVG(response_time_ms) as avg_response_time,
                        AVG(confidence) as avg_confidence,
                        AVG(search_result_count) as avg_search_results,
                        SUM(CASE WHEN used_general_knowledge = 1 THEN 1 ELSE 0 END) / COUNT(*) * 100 as general_knowledge_percentage,
                        COUNT(DISTINCT session_id) as unique_sessions,
                        COUNT(DISTINCT product) as products_used,
                        SUM(CASE WHEN interface_type = 'CLI' THEN 1 ELSE 0 END) as cli_conversations,
                        SUM(CASE WHEN interface_type = 'WEB' THEN 1 ELSE 0 END) as web_conversations
                    FROM chatbot_logs
                    WHERE created_at >= NOW() - INTERVAL %s HOUR
                    """

                    await cursor.execute(query, (hours,))
                    result = await cursor.fetchone()

                    if result:
                        return {
                            'total_conversations': result['total_conversations'],
                            'avg_response_time_ms': float(result['avg_response_time']) if result['avg_response_time'] else None,
                            'avg_confidence': float(result['avg_confidence']) if result['avg_confidence'] else None,
                            'avg_search_results': float(result['avg_search_results']) if result['avg_search_results'] else None,
                            'general_knowledge_percentage': float(result['general_knowledge_percentage']) if result['general_knowledge_percentage'] else 0,
                            'unique_sessions': result['unique_sessions'],
                            'products_used': result['products_used'],
                            'cli_conversations': result['cli_conversations'],
                            'web_conversations': result['web_conversations'],
                            'period_hours': hours
                        }

                    return {}

        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return {}

# Global database logger instance
_db_logger: Optional[DatabaseLogger] = None

async def get_database_logger() -> DatabaseLogger:
    """Get or create global database logger instance"""
    global _db_logger

    if _db_logger is None:
        _db_logger = DatabaseLogger()
        await _db_logger.initialize()

    return _db_logger

async def close_database_logger():
    """Close global database logger"""
    global _db_logger

    if _db_logger:
        await _db_logger.close()
        _db_logger = None

def extract_tool_execution_data(tool_results: List[Any]) -> Dict[str, Any]:
    """Extract tool execution data for logging"""
    if not tool_results:
        return {}

    tools_used = []
    execution_times = {}
    search_queries = []
    total_execution_time = 0

    for tool_result in tool_results:
        if hasattr(tool_result, 'tool_name'):
            tools_used.append(tool_result.tool_name)

        if hasattr(tool_result, 'execution_time_ms'):
            tool_name = getattr(tool_result, 'tool_name', 'unknown')
            execution_times[tool_name] = tool_result.execution_time_ms
            total_execution_time += tool_result.execution_time_ms

        if hasattr(tool_result, 'query') and tool_result.query:
            search_queries.append(tool_result.query)

    return {
        'tools_used': tools_used,
        'execution_time_ms': execution_times,
        'total_execution_time_ms': total_execution_time,
        'search_queries': search_queries,
        'tool_count': len(tool_results)
    }
