#!/usr/bin/env python3
"""
Esempio Pratico del Flusso di Validazione delle Richieste
Dimostra l'utilizzo del sistema di validazione in scenari reali
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from security_utils import (
    initialize_request_validation_flow, get_request_validation_flow,
    ValidationStatus, ValidationResult
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ChatbotValidationDemo:
    """Dimostra l'integrazione del flusso di validazione in un chatbot"""
    
    def __init__(self):
        # Inizializza il flusso di validazione
        self.validation_flow = initialize_request_validation_flow()
        logger.info("🤖 Chatbot con validazione inizializzato")
    
    async def process_user_request(self, user_query: str, product: str = "SYM") -> dict:
        """
        Processa una richiesta utente con validazione completa
        
        Args:
            user_query: La richiesta dell'utente
            product: Il prodotto di riferimento
            
        Returns:
            Dict con risultato della validazione e risposta
        """
        print(f"\n👤 Utente: {user_query}")
        
        # Esegui validazione
        validation_result = await self.validation_flow.validate_request(user_query, product)
        
        # Prepara risposta basata sul risultato
        if validation_result.status == ValidationStatus.APPROVATA:
            response = self._generate_approved_response(validation_result)
            print(f"✅ Richiesta approvata ({validation_result.step_completed})")
        else:
            response = self._generate_blocked_response(validation_result)
            print(f"❌ Richiesta bloccata ({validation_result.step_completed})")
        
        print(f"🤖 Bot: {response['message']}")
        
        return {
            'validation_result': validation_result,
            'response': response,
            'user_query': user_query
        }
    
    def _generate_approved_response(self, validation_result: ValidationResult) -> dict:
        """Genera risposta per richieste approvate"""
        
        if "Query sui Documenti" in validation_result.step_completed:
            message = "Ho trovato informazioni rilevanti nella documentazione tecnica. Procedo con la ricerca dettagliata..."
        elif "Analisi Whitelist" in validation_result.step_completed:
            message = f"Riconosco i termini tecnici: {', '.join(validation_result.whitelist_matches[:3])}. Procedo con l'analisi..."
        else:
            message = "La tua richiesta è stata approvata. Procedo con l'elaborazione..."
        
        return {
            'status': 'approved',
            'message': message,
            'step': validation_result.step_completed,
            'reason': validation_result.reason
        }
    
    def _generate_blocked_response(self, validation_result: ValidationResult) -> dict:
        """Genera risposta per richieste bloccate"""
        
        if "Filtro Blacklist" in validation_result.step_completed:
            message = "Mi dispiace, ma non posso elaborare richieste con contenuti inappropriati. Ti prego di riformulare la domanda in modo più appropriato."
        elif "Condizione di Fallimento" in validation_result.step_completed:
            message = "La tua richiesta non sembra essere correlata ai nostri prodotti automotive. Puoi aiutarmi con domande tecniche sui nostri veicoli?"
        else:
            message = f"Non posso elaborare questa richiesta: {validation_result.reason}"
        
        return {
            'status': 'blocked',
            'message': message,
            'step': validation_result.step_completed,
            'reason': validation_result.reason
        }


async def demo_scenarios():
    """Dimostra vari scenari di validazione"""
    
    chatbot = ChatbotValidationDemo()
    
    # Scenari di test
    test_scenarios = [
        # Richieste appropriate - dovrebbero essere approvate
        {
            'query': "Come sostituire l'olio del motore?",
            'expected': 'approved',
            'description': "Richiesta tecnica legittima"
        },
        {
            'query': "Specifiche del bilanciere per SYM 125",
            'expected': 'approved',
            'description': "Richiesta con termini tecnici specifici"
        },
        {
            'query': "Controllo delle valvole",
            'expected': 'approved',
            'description': "Richiesta di manutenzione standard"
        },
        
        # Richieste inappropriate - dovrebbero essere bloccate
        {
            'query': "Che cazzo di problema ha questo motore?",
            'expected': 'blocked',
            'description': "Linguaggio inappropriato"
        },
        {
            'query': "Dove posso comprare droga?",
            'expected': 'blocked',
            'description': "Contenuto illegale"
        },
        
        # Richieste irrilevanti - dovrebbero essere bloccate
        {
            'query': "Ricetta per la pizza margherita",
            'expected': 'blocked',
            'description': "Argomento non correlato"
        },
        {
            'query': "Previsioni del tempo per domani",
            'expected': 'blocked',
            'description': "Richiesta fuori dominio"
        },
        
        # Casi limite
        {
            'query': "Problema motore",
            'expected': 'approved',
            'description': "Richiesta breve ma tecnica"
        },
        {
            'query': "Aiuto",
            'expected': 'blocked',
            'description': "Richiesta troppo generica"
        }
    ]
    
    print("🎯 Demo Scenari di Validazione")
    print("=" * 50)
    
    results = []
    correct_predictions = 0
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 Scenario {i}: {scenario['description']}")
        
        result = await chatbot.process_user_request(scenario['query'])
        
        # Verifica predizione
        actual_status = result['response']['status']
        expected_status = scenario['expected']
        is_correct = actual_status == expected_status
        
        if is_correct:
            correct_predictions += 1
            print("✅ Predizione corretta")
        else:
            print(f"❌ Predizione errata - Atteso: {expected_status}, Ottenuto: {actual_status}")
        
        results.append({
            'scenario': scenario,
            'result': result,
            'correct': is_correct
        })
    
    # Statistiche finali
    accuracy = (correct_predictions / len(test_scenarios)) * 100
    print(f"\n📊 Risultati Finali")
    print("=" * 30)
    print(f"Scenari testati: {len(test_scenarios)}")
    print(f"Predizioni corrette: {correct_predictions}")
    print(f"Accuratezza: {accuracy:.1f}%")
    
    return results


async def demo_real_time_interaction():
    """Dimostra interazione in tempo reale"""
    
    print("\n🎮 Demo Interazione in Tempo Reale")
    print("=" * 40)
    print("Digita 'quit' per uscire")
    
    chatbot = ChatbotValidationDemo()
    
    while True:
        try:
            user_input = input("\n👤 Tu: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Arrivederci!")
                break
            
            if not user_input:
                continue
            
            await chatbot.process_user_request(user_input)
            
        except KeyboardInterrupt:
            print("\n👋 Arrivederci!")
            break
        except Exception as e:
            print(f"❌ Errore: {e}")


async def demo_statistics():
    """Mostra statistiche del sistema di validazione"""
    
    print("\n📈 Statistiche Sistema di Validazione")
    print("=" * 40)
    
    validation_flow = get_request_validation_flow()
    if not validation_flow:
        print("❌ Sistema di validazione non inizializzato")
        return
    
    stats = validation_flow.get_validation_stats()
    
    print(f"📝 Termini in blacklist: {stats['blacklist_size']}")
    print(f"🔍 Document processor: {'✅' if stats['document_processor_available'] else '❌'}")
    print(f"📋 Domain validator: {'✅' if stats['domain_validator_available'] else '❌'}")
    
    if 'domain_keywords_size' in stats:
        print(f"🏷️  Keywords di dominio: {stats['domain_keywords_size']}")
    
    if 'document_whitelist_size' in stats:
        print(f"📚 Termini da documenti: {stats['document_whitelist_size']}")
    
    # Mostra alcuni termini della blacklist
    blacklist_sample = list(validation_flow.get_blacklist_terms())[:10]
    print(f"\n🚫 Esempi blacklist: {', '.join(blacklist_sample)}")


async def main():
    """Funzione principale della demo"""
    
    print("🚀 Demo Flusso di Validazione delle Richieste")
    print("=" * 50)
    
    try:
        # Demo scenari automatici
        await demo_scenarios()
        
        # Statistiche sistema
        await demo_statistics()
        
        # Chiedi se vuole provare l'interazione in tempo reale
        print(f"\n❓ Vuoi provare l'interazione in tempo reale? (y/n): ", end="")
        choice = input().strip().lower()
        
        if choice in ['y', 'yes', 's', 'si']:
            await demo_real_time_interaction()
        
        print("\n✅ Demo completata con successo!")
        
    except Exception as e:
        print(f"\n❌ Errore durante la demo: {e}")
        logger.exception("Errore nella demo")


if __name__ == "__main__":
    asyncio.run(main())
