#!/usr/bin/env python3
"""
Optimization and Performance Tuning Script
Analyzes system performance and provides optimization recommendations
"""

import asyncio
import time
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any
import json

# Optional import for system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Warning: psutil not available. System resource analysis will be limited.")
    print("Install with: pip install psutil")

# Add project root to path and change working directory
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Change to project root directory for correct relative paths
import os
os.chdir(str(project_root))

from config import config

logger = logging.getLogger(__name__)

class PerformanceAnalyzer:
    """Analyzes system performance and provides optimization recommendations"""
    
    def __init__(self):
        self.metrics = {}
        self.recommendations = []
    
    async def analyze_system(self):
        """Run comprehensive performance analysis"""
        print("🔍 Analyzing system performance...")
        
        # System resource analysis
        await self.analyze_system_resources()
        
        # Document analysis
        await self.analyze_documents()
        
        # Configuration analysis
        await self.analyze_configuration()
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Create performance report
        self.create_performance_report()
    
    async def analyze_system_resources(self):
        """Analyze system resources"""
        print("📊 Analyzing system resources...")

        if not PSUTIL_AVAILABLE:
            print("⚠️  System resource analysis skipped (psutil not available)")
            self.metrics['memory'] = {
                'status': 'unavailable',
                'message': 'Install psutil for detailed system analysis'
            }
            self.metrics['cpu'] = {
                'status': 'unavailable',
                'message': 'Install psutil for detailed system analysis'
            }
            self.metrics['disk'] = {
                'status': 'unavailable',
                'message': 'Install psutil for detailed system analysis'
            }
            return

        # Memory analysis
        memory = psutil.virtual_memory()
        self.metrics['memory'] = {
            'total_gb': round(memory.total / (1024**3), 2),
            'available_gb': round(memory.available / (1024**3), 2),
            'percent_used': memory.percent,
            'recommended_min_gb': 4
        }

        # CPU analysis
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics['cpu'] = {
            'cores': cpu_count,
            'current_usage_percent': cpu_percent,
            'recommended_min_cores': 2
        }

        # Disk analysis
        disk = psutil.disk_usage('.')
        self.metrics['disk'] = {
            'total_gb': round(disk.total / (1024**3), 2),
            'free_gb': round(disk.free / (1024**3), 2),
            'percent_used': round((disk.used / disk.total) * 100, 2)
        }
    
    async def analyze_documents(self):
        """Analyze document structure and size"""
        print("📚 Analyzing documents...")
        
        total_docs = 0
        total_size_mb = 0
        products = {}
        
        sorgenti_path = config.sorgenti_path
        
        for product_dir in sorgenti_path.iterdir():
            if not product_dir.is_dir() or product_dir.name.startswith('.'):
                continue
            
            product_name = product_dir.name
            products[product_name] = {
                'link_docs': 0,
                'nolink_docs': 0,
                'total_size_mb': 0
            }
            
            # Analyze link documents
            link_dir = product_dir / 'link'
            if link_dir.exists():
                for pdf_file in link_dir.glob('*.pdf'):
                    products[product_name]['link_docs'] += 1
                    products[product_name]['total_size_mb'] += pdf_file.stat().st_size / (1024**2)
                    total_docs += 1
                    total_size_mb += pdf_file.stat().st_size / (1024**2)
            
            # Analyze nolink documents
            nolink_dir = product_dir / 'nolink'
            if nolink_dir.exists():
                for pdf_file in nolink_dir.glob('*.pdf'):
                    products[product_name]['nolink_docs'] += 1
                    products[product_name]['total_size_mb'] += pdf_file.stat().st_size / (1024**2)
                    total_docs += 1
                    total_size_mb += pdf_file.stat().st_size / (1024**2)
            
            products[product_name]['total_size_mb'] = round(products[product_name]['total_size_mb'], 2)
        
        self.metrics['documents'] = {
            'total_documents': total_docs,
            'total_size_mb': round(total_size_mb, 2),
            'products': products,
            'avg_doc_size_mb': round(total_size_mb / total_docs, 2) if total_docs > 0 else 0
        }
    
    async def analyze_configuration(self):
        """Analyze current configuration settings"""
        print("⚙️ Analyzing configuration...")
        
        self.metrics['configuration'] = {
            'max_context_tokens': config.max_context_tokens,
            'chunk_size': config.chunk_size,
            'overlap_size': config.overlap_size,
            'cache_ttl': config.cache_ttl,
            'max_file_size_mb': config.max_file_size_mb,
            'max_concurrent_docs': config.max_concurrent_docs,
            'gemini_temperature': config.gemini_temperature,
            'gemini_max_output_tokens': config.gemini_max_output_tokens
        }
    
    def generate_recommendations(self):
        """Generate optimization recommendations"""
        print("💡 Generating recommendations...")

        # Memory recommendations (only if psutil is available)
        memory = self.metrics['memory']
        if memory.get('status') != 'unavailable' and memory.get('available_gb', 0) < 2:
            self.recommendations.append({
                'category': 'Memory',
                'priority': 'High',
                'issue': f"Low available memory: {memory['available_gb']}GB",
                'recommendation': 'Consider reducing MAX_CONCURRENT_DOCS or CHUNK_SIZE in .env'
            })
        elif memory.get('status') == 'unavailable':
            self.recommendations.append({
                'category': 'System Monitoring',
                'priority': 'Low',
                'issue': 'System resource monitoring unavailable',
                'recommendation': 'Install psutil for detailed system analysis: pip install psutil'
            })
        
        # Document size recommendations
        docs = self.metrics['documents']
        if docs['avg_doc_size_mb'] > 10:
            self.recommendations.append({
                'category': 'Documents',
                'priority': 'Medium',
                'issue': f"Large average document size: {docs['avg_doc_size_mb']}MB",
                'recommendation': 'Consider splitting large documents or increasing MAX_FILE_SIZE_MB'
            })
        
        # Configuration recommendations
        config_metrics = self.metrics['configuration']
        
        if config_metrics['chunk_size'] > 1500:
            self.recommendations.append({
                'category': 'Configuration',
                'priority': 'Low',
                'issue': f"Large chunk size: {config_metrics['chunk_size']}",
                'recommendation': 'Consider reducing CHUNK_SIZE for better semantic search accuracy'
            })
        
        if config_metrics['max_context_tokens'] > 10000:
            self.recommendations.append({
                'category': 'Configuration',
                'priority': 'Medium',
                'issue': f"High context tokens: {config_metrics['max_context_tokens']}",
                'recommendation': 'Consider reducing MAX_CONTEXT_TOKENS to improve response time'
            })
        
        # Performance recommendations based on document count
        if docs['total_documents'] > 50:
            self.recommendations.append({
                'category': 'Performance',
                'priority': 'Medium',
                'issue': f"Many documents: {docs['total_documents']}",
                'recommendation': 'Consider implementing document pre-processing or increasing cache TTL'
            })
        
        # Add positive feedback if system is well configured
        if not self.recommendations:
            self.recommendations.append({
                'category': 'System',
                'priority': 'Info',
                'issue': 'System appears well configured',
                'recommendation': 'No immediate optimizations needed'
            })
    
    def create_performance_report(self):
        """Create comprehensive performance report"""
        print("\n" + "="*80)
        print("📊 PERFORMANCE ANALYSIS REPORT")
        print("="*80)
        
        # System Resources
        print("\n🖥️  SYSTEM RESOURCES")
        print("-" * 40)
        memory = self.metrics['memory']
        if memory.get('status') == 'unavailable':
            print(f"Memory: {memory['message']}")
        else:
            print(f"Memory: {memory['available_gb']:.1f}GB available / {memory['total_gb']:.1f}GB total ({memory['percent_used']:.1f}% used)")

        cpu = self.metrics['cpu']
        if cpu.get('status') == 'unavailable':
            print(f"CPU: {cpu['message']}")
        else:
            print(f"CPU: {cpu['cores']} cores, {cpu['current_usage_percent']:.1f}% current usage")

        disk = self.metrics['disk']
        if disk.get('status') == 'unavailable':
            print(f"Disk: {disk['message']}")
        else:
            print(f"Disk: {disk['free_gb']:.1f}GB free / {disk['total_gb']:.1f}GB total ({disk['percent_used']:.1f}% used)")
        
        # Documents
        print("\n📚 DOCUMENT ANALYSIS")
        print("-" * 40)
        docs = self.metrics['documents']
        print(f"Total Documents: {docs['total_documents']}")
        print(f"Total Size: {docs['total_size_mb']:.1f}MB")
        print(f"Average Document Size: {docs['avg_doc_size_mb']:.1f}MB")
        
        print("\nProducts:")
        for product, data in docs['products'].items():
            total_docs = data['link_docs'] + data['nolink_docs']
            print(f"  {product}: {total_docs} docs ({data['link_docs']} link, {data['nolink_docs']} nolink) - {data['total_size_mb']:.1f}MB")
        
        # Configuration
        print("\n⚙️  CONFIGURATION")
        print("-" * 40)
        config_metrics = self.metrics['configuration']
        print(f"Context Tokens: {config_metrics['max_context_tokens']}")
        print(f"Chunk Size: {config_metrics['chunk_size']}")
        print(f"Overlap Size: {config_metrics['overlap_size']}")
        print(f"Cache TTL: {config_metrics['cache_ttl']}s")
        print(f"Max File Size: {config_metrics['max_file_size_mb']}MB")
        print(f"Max Concurrent Docs: {config_metrics['max_concurrent_docs']}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS")
        print("-" * 40)
        
        if not self.recommendations:
            print("✅ No recommendations - system is optimally configured!")
        else:
            for i, rec in enumerate(self.recommendations, 1):
                priority_icon = {"High": "🔴", "Medium": "🟡", "Low": "🟢", "Info": "ℹ️"}.get(rec['priority'], "•")
                print(f"{i}. {priority_icon} [{rec['category']}] {rec['priority']} Priority")
                print(f"   Issue: {rec['issue']}")
                print(f"   Recommendation: {rec['recommendation']}\n")
        
        # Optimal Settings Suggestions
        print("\n🎯 SUGGESTED OPTIMAL SETTINGS")
        print("-" * 40)
        
        # Calculate optimal settings based on system resources
        memory = self.metrics['memory']
        if memory.get('status') == 'unavailable':
            print("⚠️  Cannot provide optimal settings without system resource information")
            print("   Install psutil for detailed recommendations: pip install psutil")
            return

        memory_gb = memory['available_gb']
        doc_count = self.metrics['documents']['total_documents']
        avg_doc_size = self.metrics['documents']['avg_doc_size_mb']

        # Suggest chunk size based on document size
        if avg_doc_size < 5:
            suggested_chunk_size = 800
        elif avg_doc_size < 15:
            suggested_chunk_size = 1000
        else:
            suggested_chunk_size = 1200

        # Suggest concurrent docs based on memory
        if memory_gb < 4:
            suggested_concurrent = 5
        elif memory_gb < 8:
            suggested_concurrent = 10
        else:
            suggested_concurrent = 15
        
        # Suggest context tokens based on use case
        if doc_count < 20:
            suggested_context = 6000
        elif doc_count < 50:
            suggested_context = 8000
        else:
            suggested_context = 10000
        
        print(f"CHUNK_SIZE={suggested_chunk_size}")
        print(f"MAX_CONCURRENT_DOCS={suggested_concurrent}")
        print(f"MAX_CONTEXT_TOKENS={suggested_context}")
        print(f"CACHE_TTL={3600 if doc_count < 30 else 7200}")
        print(f"OVERLAP_SIZE={suggested_chunk_size // 5}")
        
        print("\n" + "="*80)
        
        # Save metrics to file
        self.save_metrics()
    
    def save_metrics(self):
        """Save performance metrics to file"""
        try:
            metrics_file = Path('performance_metrics.json')
            
            report_data = {
                'timestamp': time.time(),
                'metrics': self.metrics,
                'recommendations': self.recommendations
            }
            
            with open(metrics_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"📄 Performance metrics saved to {metrics_file}")
            
        except Exception as e:
            print(f"❌ Failed to save metrics: {e}")

async def main():
    """Main optimization function"""
    try:
        analyzer = PerformanceAnalyzer()
        await analyzer.analyze_system()
        
        print("\n🎉 Performance analysis complete!")
        print("Review the recommendations above to optimize your system.")
        
    except Exception as e:
        print(f"❌ Performance analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
