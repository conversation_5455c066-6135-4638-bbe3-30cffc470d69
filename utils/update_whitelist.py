#!/usr/bin/env python3
"""
Utility script to manually update the document whitelist
"""

import asyncio
import logging
import argparse
import sys
from pathlib import Path

# Add project root to path and change working directory
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Change to project root directory for correct relative paths
import os
os.chdir(str(project_root))

from security_utils import domain_validator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def update_whitelist(force=False):
    """Update the document whitelist"""
    print("=== Document Whitelist Update ===")
    
    # Get initial stats
    stats = domain_validator.get_whitelist_stats()
    print(f"Current whitelist size: {stats['whitelist_size']}")
    print(f"Last update: {stats['last_update']}")
    
    if force:
        print("Forcing whitelist update...")
        await domain_validator.force_whitelist_update()
    else:
        print("Checking for whitelist update...")
        await domain_validator.update_document_whitelist()
    
    # Get updated stats
    new_stats = domain_validator.get_whitelist_stats()
    print(f"Updated whitelist size: {new_stats['whitelist_size']}")
    print(f"New terms added: {new_stats['whitelist_size'] - stats['whitelist_size']}")
    print(f"Last update: {new_stats['last_update']}")
    
    if new_stats['sample_terms']:
        print(f"Sample terms: {', '.join(new_stats['sample_terms'])}")

async def show_stats():
    """Show whitelist statistics"""
    print("=== Document Whitelist Statistics ===")
    
    stats = domain_validator.get_whitelist_stats()
    print(f"Whitelist size: {stats['whitelist_size']}")
    print(f"Last update: {stats['last_update']}")
    print(f"Cache file exists: {stats['cache_file_exists']}")
    
    if stats['sample_terms']:
        print(f"Sample terms: {', '.join(stats['sample_terms'])}")
    
    # Show some specific terms
    specific_terms = []
    for term in domain_validator.document_whitelist:
        if any(keyword in term for keyword in ['silkolene', 'synerject', 'symphony', 'castrol', 'motul']):
            specific_terms.append(term)
        if len(specific_terms) >= 20:
            break
    
    if specific_terms:
        print(f"\nBrand/Product terms found:")
        for term in specific_terms:
            print(f"  - {term}")

async def test_query(query):
    """Test a specific query against the whitelist"""
    print(f"=== Testing Query: '{query}' ===")
    
    is_relevant, error = domain_validator.validate_domain_relevance(query)
    
    if is_relevant:
        print("✓ ALLOWED - Query is considered automotive-relevant")
    else:
        print("✗ BLOCKED - Query is not considered automotive-relevant")
        if error:
            print(f"  Reason: {error}")

async def main():
    parser = argparse.ArgumentParser(description='Document whitelist management utility')
    parser.add_argument('action', choices=['update', 'force-update', 'stats', 'test'], 
                       help='Action to perform')
    parser.add_argument('--query', help='Query to test (for test action)')
    
    args = parser.parse_args()
    
    if args.action == 'update':
        await update_whitelist(force=False)
    elif args.action == 'force-update':
        await update_whitelist(force=True)
    elif args.action == 'stats':
        await show_stats()
    elif args.action == 'test':
        if not args.query:
            print("Error: --query is required for test action")
            return
        await test_query(args.query)

if __name__ == "__main__":
    asyncio.run(main())
