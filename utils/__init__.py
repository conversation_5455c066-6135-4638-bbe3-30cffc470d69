"""
Utility modules for Softway Chatbot SYM

This package contains reusable utility functions and tools for:
- Cache management and optimization
- Database monitoring and analytics
- System performance analysis
- Whitelist management
"""

import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))

__version__ = "1.0.0"
__author__ = "Softway Chatbot SYM Team"

# Import main utility classes for easy access
try:
    from .cache_utils import CacheUtilities
    # Note: Other classes need to be imported directly from their modules
    # due to their specific implementation patterns
except ImportError:
    # Handle cases where dependencies might not be available
    pass

__all__ = [
    'CacheUtilities'
]
