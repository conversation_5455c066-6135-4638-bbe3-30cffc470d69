#!/usr/bin/env python3
"""
Domain Configuration Script - Configura il sistema per un dominio specifico
Sostituisce la dipendenza hardcoded da AUTOMOTIVE
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from domain_config import get_available_domains, get_domain_config
from domain_validation_config import initialize_domain_validation_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DomainConfigurator:
    """Gestisce la configurazione del dominio per il sistema"""
    
    def __init__(self):
        self.env_file = Path('.env')
        self.available_domains = get_available_domains()
    
    def list_available_domains(self) -> None:
        """Mostra i domini disponibili"""
        print("🌐 Domini disponibili:")
        print("=" * 40)
        
        for domain in self.available_domains:
            try:
                config = get_domain_config(domain)
                print(f"📋 {domain}")
                print(f"   Nome: {config.display_name}")
                print(f"   Descrizione: {config.description}")
                print(f"   Keywords: {len(config.keywords)} termini")
                print(f"   Non-keywords: {len(config.non_keywords)} termini")
                print()
            except Exception as e:
                print(f"❌ {domain}: Errore nel caricamento ({e})")
                print()
    
    def get_current_domain(self) -> Optional[str]:
        """Ottiene il dominio attualmente configurato"""
        try:
            from config import config
            return config.system_scope
        except Exception as e:
            logger.warning(f"Could not get current domain: {e}")
            return os.getenv('SYSTEM_SCOPE', '').upper()
    
    def set_domain(self, domain: str, multi_domain: List[str] = None) -> bool:
        """Configura il sistema per un dominio specifico"""
        try:
            domain = domain.upper()
            
            # Valida il dominio
            if domain not in self.available_domains:
                print(f"❌ Dominio '{domain}' non disponibile.")
                print(f"Domini disponibili: {', '.join(self.available_domains)}")
                return False
            
            # Prepara la configurazione
            if multi_domain:
                # Multi-dominio
                all_domains = [domain] + [d.upper() for d in multi_domain if d.upper() != domain]
                valid_domains = [d for d in all_domains if d in self.available_domains]
                
                if len(valid_domains) != len(all_domains):
                    invalid = set(all_domains) - set(valid_domains)
                    print(f"❌ Domini non validi: {', '.join(invalid)}")
                    return False
                
                domain_config = ','.join(valid_domains)
                print(f"🔧 Configurazione multi-dominio: {domain_config}")
            else:
                # Singolo dominio
                domain_config = domain
                print(f"🔧 Configurazione singolo dominio: {domain_config}")
            
            # Aggiorna variabile d'ambiente
            self._update_env_file('SYSTEM_SCOPE', domain_config)
            
            # Testa la configurazione
            if self._test_configuration(domain_config):
                print(f"✅ Dominio configurato con successo: {domain_config}")
                print(f"🔄 Riavvia l'applicazione per applicare le modifiche")
                return True
            else:
                print(f"❌ Errore nella configurazione del dominio")
                return False
                
        except Exception as e:
            logger.error(f"Error setting domain: {e}")
            print(f"❌ Errore nella configurazione: {e}")
            return False
    
    def _update_env_file(self, key: str, value: str) -> None:
        """Aggiorna il file .env con la nuova configurazione"""
        env_lines = []
        key_found = False
        
        # Leggi il file esistente se presente
        if self.env_file.exists():
            with open(self.env_file, 'r') as f:
                env_lines = f.readlines()
        
        # Aggiorna o aggiungi la chiave
        for i, line in enumerate(env_lines):
            if line.strip().startswith(f'{key}='):
                env_lines[i] = f'{key}={value}\n'
                key_found = True
                break
        
        if not key_found:
            env_lines.append(f'{key}={value}\n')
        
        # Scrivi il file aggiornato
        with open(self.env_file, 'w') as f:
            f.writelines(env_lines)
        
        print(f"📝 Aggiornato {self.env_file}: {key}={value}")
    
    def _test_configuration(self, domain_config: str) -> bool:
        """Testa la configurazione del dominio"""
        try:
            # Imposta temporaneamente la variabile d'ambiente
            original_value = os.getenv('SYSTEM_SCOPE')
            os.environ['SYSTEM_SCOPE'] = domain_config
            
            # Testa il caricamento della configurazione
            from config import Config
            test_config = Config()

            primary_domain = test_config.system_scope
            all_domains = test_config.system_scopes
            
            print(f"🧪 Test configurazione:")
            print(f"   Dominio primario: {primary_domain}")
            print(f"   Tutti i domini: {', '.join(all_domains)}")
            
            # Testa il domain validation manager
            validation_manager = initialize_domain_validation_manager()
            available_validation_domains = validation_manager.get_available_domains()
            
            print(f"   Domini validazione disponibili: {len(available_validation_domains)}")
            
            # Ripristina la variabile d'ambiente originale
            if original_value is not None:
                os.environ['SYSTEM_SCOPE'] = original_value
            elif 'SYSTEM_SCOPE' in os.environ:
                del os.environ['SYSTEM_SCOPE']
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration test failed: {e}")
            return False
    
    def show_current_configuration(self) -> None:
        """Mostra la configurazione attuale"""
        print("🔍 Configurazione Attuale:")
        print("=" * 30)
        
        current_domain = self.get_current_domain()
        if current_domain:
            print(f"📋 Dominio configurato: {current_domain}")
            
            try:
                config = get_domain_config(current_domain)
                print(f"   Nome: {config.display_name}")
                print(f"   Descrizione: {config.description}")
                print(f"   Keywords: {len(config.keywords)} termini")
                print(f"   Non-keywords: {len(config.non_keywords)} termini")
            except Exception as e:
                print(f"   ❌ Errore nel caricamento configurazione: {e}")
        else:
            print("❌ Nessun dominio configurato")
            print("💡 Usa 'python configure_domain.py set <DOMAIN>' per configurare")
        
        # Mostra variabili d'ambiente
        env_scope = os.getenv('SYSTEM_SCOPE', 'Non impostato')
        print(f"🌍 SYSTEM_SCOPE: {env_scope}")
        
        # Mostra file .env
        if self.env_file.exists():
            print(f"📁 File .env: {self.env_file} (esiste)")
        else:
            print(f"📁 File .env: {self.env_file} (non esiste)")


def main():
    """Funzione principale dello script"""
    parser = argparse.ArgumentParser(
        description="Configura il dominio per il sistema chatbot",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Esempi:
  python configure_domain.py list                    # Lista domini disponibili
  python configure_domain.py current                 # Mostra configurazione attuale
  python configure_domain.py set AUTOMOTIVE          # Configura dominio automotive
  python configure_domain.py set FINANCE             # Configura dominio finance
  python configure_domain.py set AUTOMOTIVE FINANCE  # Multi-dominio
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandi disponibili')
    
    # Comando list
    subparsers.add_parser('list', help='Lista domini disponibili')
    
    # Comando current
    subparsers.add_parser('current', help='Mostra configurazione attuale')
    
    # Comando set
    set_parser = subparsers.add_parser('set', help='Configura dominio')
    set_parser.add_argument('domain', help='Dominio primario da configurare')
    set_parser.add_argument('additional_domains', nargs='*', 
                           help='Domini aggiuntivi per configurazione multi-dominio')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    configurator = DomainConfigurator()
    
    try:
        if args.command == 'list':
            configurator.list_available_domains()
        
        elif args.command == 'current':
            configurator.show_current_configuration()
        
        elif args.command == 'set':
            success = configurator.set_domain(args.domain, args.additional_domains)
            sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n👋 Operazione annullata dall'utente")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Errore imprevisto: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
