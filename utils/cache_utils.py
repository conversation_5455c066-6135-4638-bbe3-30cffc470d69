#!/usr/bin/env python3
"""
Cache Management Utilities
Command-line tools for managing document processing cache
"""

import asyncio
import argparse
import logging
import sys
from pathlib import Path
from typing import List, Optional

# Add project root to path and change working directory
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Change to project root directory for correct relative paths
import os
os.chdir(str(project_root))

from cache_manager import create_cache_manager, DocumentCacheManager
from cache_invalidator import CacheInvalidator, ScheduledCacheCleanup
from config import config

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CacheUtilities:
    """Utility class for cache management operations"""
    
    def __init__(self):
        self.cache_manager = create_cache_manager(
            cache_type=config.cache_type,
            cache_dir=config.cache_dir,
            max_size_mb=config.cache_max_size_mb,
            ttl_hours=config.cache_ttl_hours,
            redis_url=getattr(config, 'redis_url', 'redis://localhost:6379'),
            redis_db=getattr(config, 'redis_db', 0),
            key_prefix=getattr(config, 'redis_key_prefix', 'docache:')
        )
    
    async def show_stats(self):
        """Display cache statistics"""
        print("📊 Cache Statistics")
        print("=" * 50)
        
        try:
            stats = await self.cache_manager.get_stats()
            
            print(f"Total entries: {stats.total_entries}")
            print(f"Total size: {stats.total_size_mb:.2f} MB")
            print(f"Hit count: {stats.hit_count}")
            print(f"Miss count: {stats.miss_count}")
            print(f"Hit rate: {stats.hit_rate:.2%}")
            print(f"Eviction count: {stats.eviction_count}")
            
            # Show cache type and configuration
            print(f"\nCache Configuration:")
            print(f"Type: {config.cache_type}")
            print(f"Max size: {config.cache_max_size_mb} MB")
            print(f"TTL: {config.cache_ttl_hours} hours")
            
            if config.cache_type == "filesystem":
                print(f"Cache directory: {config.cache_dir}")
            elif config.cache_type == "redis":
                print(f"Redis URL: {getattr(config, 'redis_url', 'redis://localhost:6379')}")
                print(f"Redis DB: {getattr(config, 'redis_db', 0)}")
            
        except Exception as e:
            print(f"❌ Error getting cache stats: {e}")
    
    async def list_entries(self, limit: Optional[int] = None):
        """List cache entries"""
        print("📋 Cache Entries")
        print("=" * 50)
        
        try:
            keys = await self.cache_manager.backend.keys()
            
            if not keys:
                print("No cache entries found")
                return
            
            # Group keys by type
            entry_types = {}
            for key in keys:
                for prefix, name in self.cache_manager.PREFIXES.items():
                    if key.startswith(name):
                        if prefix not in entry_types:
                            entry_types[prefix] = []
                        entry_types[prefix].append(key)
                        break
            
            # Display entries by type
            for entry_type, type_keys in entry_types.items():
                print(f"\n{entry_type.upper()} ({len(type_keys)} entries):")
                
                display_keys = type_keys[:limit] if limit else type_keys
                
                for key in display_keys:
                    try:
                        entry = await self.cache_manager.backend.get(key)
                        if entry:
                            age = (entry.last_accessed - entry.created_at).total_seconds() / 3600
                            print(f"  • {key[:60]}... (age: {age:.1f}h, accessed: {entry.access_count}x)")
                    except Exception as e:
                        print(f"  • {key[:60]}... (error: {e})")
                
                if limit and len(type_keys) > limit:
                    print(f"  ... and {len(type_keys) - limit} more entries")
            
        except Exception as e:
            print(f"❌ Error listing cache entries: {e}")
    
    async def cleanup(self):
        """Perform cache cleanup"""
        print("🧹 Performing Cache Cleanup")
        print("=" * 50)
        
        try:
            # Get stats before cleanup
            stats_before = await self.cache_manager.get_stats()
            print(f"Before cleanup: {stats_before.total_entries} entries, {stats_before.total_size_mb:.2f} MB")
            
            # Perform cleanup
            await self.cache_manager.cleanup_expired()
            await self.cache_manager.enforce_size_limit()
            
            # Get stats after cleanup
            stats_after = await self.cache_manager.get_stats()
            print(f"After cleanup: {stats_after.total_entries} entries, {stats_after.total_size_mb:.2f} MB")
            
            entries_removed = stats_before.total_entries - stats_after.total_entries
            size_freed = stats_before.total_size_mb - stats_after.total_size_mb
            
            print(f"✅ Cleanup completed:")
            print(f"   Entries removed: {entries_removed}")
            print(f"   Space freed: {size_freed:.2f} MB")
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
    
    async def clear_all(self, confirm: bool = False):
        """Clear all cache entries"""
        if not confirm:
            response = input("⚠️  This will delete ALL cache entries. Are you sure? (y/N): ")
            if response.lower() != 'y':
                print("Operation cancelled")
                return
        
        print("🗑️  Clearing All Cache Entries")
        print("=" * 50)
        
        try:
            stats_before = await self.cache_manager.get_stats()
            print(f"Clearing {stats_before.total_entries} entries ({stats_before.total_size_mb:.2f} MB)")
            
            await self.cache_manager.clear_all()
            
            print("✅ All cache entries cleared")
            
        except Exception as e:
            print(f"❌ Error clearing cache: {e}")
    
    async def invalidate_file(self, file_path: Path):
        """Invalidate cache for a specific file"""
        print(f"🔄 Invalidating Cache for: {file_path}")
        print("=" * 50)
        
        try:
            if not file_path.exists():
                print(f"⚠️  File does not exist: {file_path}")
            
            await self.cache_manager.invalidate_file_cache(file_path)
            print("✅ Cache invalidated successfully")
            
        except Exception as e:
            print(f"❌ Error invalidating cache: {e}")
    
    async def monitor_changes(self, watch_paths: List[Path], duration_minutes: int = 60):
        """Monitor file changes and show cache invalidations"""
        print(f"👁️  Monitoring File Changes for {duration_minutes} minutes")
        print("=" * 50)
        
        try:
            invalidator = CacheInvalidator(self.cache_manager, watch_paths)
            invalidator.start_monitoring()
            
            print(f"Monitoring paths:")
            for path in watch_paths:
                print(f"  • {path}")
            
            print(f"\nWatching for changes... (Ctrl+C to stop)")
            
            # Monitor for specified duration
            await asyncio.sleep(duration_minutes * 60)
            
            invalidator.stop_monitoring()
            
            # Show pending invalidations
            pending = invalidator.get_pending_invalidations()
            if pending:
                print(f"\n📋 Files that triggered cache invalidation:")
                for file_path in pending:
                    print(f"  • {file_path}")
            else:
                print(f"\n✅ No cache invalidations during monitoring period")
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Error during monitoring: {e}")
    
    async def benchmark_cache(self, test_file: Optional[Path] = None):
        """Benchmark cache performance"""
        print("⚡ Cache Performance Benchmark")
        print("=" * 50)
        
        try:
            if not test_file:
                # Find a test file
                sorgenti_path = config.sorgenti_path
                pdf_files = list(sorgenti_path.rglob("*.pdf"))
                if not pdf_files:
                    print("❌ No PDF files found for benchmarking")
                    return
                test_file = pdf_files[0]
            
            print(f"Test file: {test_file}")
            
            # Import DocumentProcessor for testing
            from document_processor import DocumentProcessor
            
            # Test without cache
            print("\n🔄 Testing without cache...")
            processor_no_cache = DocumentProcessor(enable_cache=False)
            
            import time
            start_time = time.time()
            
            # Create a mock resource
            from mcp_server import MCPResource
            resource = MCPResource(
                name=f"test/{test_file.name}",
                uri=f"file://{test_file.absolute()}",
                description="Test document",
                metadata={
                    'file_path': str(test_file.absolute()),
                    'product': 'TEST',
                    'filename': test_file.name,
                    'citation_required': True
                }
            )
            
            result_no_cache = await processor_no_cache.process_resource(resource)
            time_no_cache = time.time() - start_time
            
            if not result_no_cache:
                print("❌ Failed to process test file")
                return
            
            print(f"   Time without cache: {time_no_cache:.2f}s")
            print(f"   Chunks created: {len(result_no_cache.chunks)}")
            
            # Test with cache (first run - cache miss)
            print("\n🔄 Testing with cache (first run)...")
            processor_with_cache = DocumentProcessor(enable_cache=True)
            
            start_time = time.time()
            result_with_cache_1 = await processor_with_cache.process_resource(resource)
            time_with_cache_1 = time.time() - start_time
            
            print(f"   Time with cache (miss): {time_with_cache_1:.2f}s")
            
            # Test with cache (second run - cache hit)
            print("\n🔄 Testing with cache (second run)...")
            
            start_time = time.time()
            result_with_cache_2 = await processor_with_cache.process_resource(resource)
            time_with_cache_2 = time.time() - start_time
            
            print(f"   Time with cache (hit): {time_with_cache_2:.2f}s")
            
            # Calculate improvements
            cache_miss_overhead = ((time_with_cache_1 - time_no_cache) / time_no_cache) * 100
            cache_hit_improvement = ((time_no_cache - time_with_cache_2) / time_no_cache) * 100
            
            print(f"\n📊 Benchmark Results:")
            print(f"   Cache miss overhead: {cache_miss_overhead:+.1f}%")
            print(f"   Cache hit improvement: {cache_hit_improvement:.1f}%")
            print(f"   Speedup factor: {time_no_cache / time_with_cache_2:.1f}x")
            
        except Exception as e:
            print(f"❌ Error during benchmark: {e}")

async def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description="Document Processing Cache Management Utilities")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Stats command
    subparsers.add_parser('stats', help='Show cache statistics')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List cache entries')
    list_parser.add_argument('--limit', type=int, help='Limit number of entries to show per type')
    
    # Cleanup command
    subparsers.add_parser('cleanup', help='Perform cache cleanup')
    
    # Clear command
    clear_parser = subparsers.add_parser('clear', help='Clear all cache entries')
    clear_parser.add_argument('--yes', action='store_true', help='Skip confirmation prompt')
    
    # Invalidate command
    invalidate_parser = subparsers.add_parser('invalidate', help='Invalidate cache for a file')
    invalidate_parser.add_argument('file', type=Path, help='File path to invalidate')
    
    # Monitor command
    monitor_parser = subparsers.add_parser('monitor', help='Monitor file changes')
    monitor_parser.add_argument('--duration', type=int, default=60, help='Duration in minutes (default: 60)')
    monitor_parser.add_argument('paths', nargs='*', type=Path, help='Paths to monitor (default: sorgenti)')
    
    # Benchmark command
    benchmark_parser = subparsers.add_parser('benchmark', help='Benchmark cache performance')
    benchmark_parser.add_argument('--file', type=Path, help='Test file (default: auto-select)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    utils = CacheUtilities()
    
    try:
        if args.command == 'stats':
            await utils.show_stats()
        
        elif args.command == 'list':
            await utils.list_entries(args.limit)
        
        elif args.command == 'cleanup':
            await utils.cleanup()
        
        elif args.command == 'clear':
            await utils.clear_all(args.yes)
        
        elif args.command == 'invalidate':
            await utils.invalidate_file(args.file)
        
        elif args.command == 'monitor':
            watch_paths = args.paths if args.paths else [config.sorgenti_path]
            await utils.monitor_changes(watch_paths, args.duration)
        
        elif args.command == 'benchmark':
            await utils.benchmark_cache(args.file)
    
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Detailed error information:")

if __name__ == "__main__":
    asyncio.run(main())
