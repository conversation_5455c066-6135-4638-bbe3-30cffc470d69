# 🛠️ Utilities Directory

This directory contains essential utilities for managing, testing, and configuring the Softway Chatbot SYM system.

## 📁 Available Utilities

### **System Management**

#### `cache_utils.py`
Cache management and optimization utility.
```bash
python cache_utils.py --stats    # View cache statistics
python cache_utils.py --clear    # Clear cache
python cache_utils.py --optimize # Optimize cache
```

#### `monitor_database.py`
Real-time database monitoring for conversation logs.
```bash
python monitor_database.py       # Start monitoring
```

#### `optimize.py`
System performance analysis and optimization recommendations.
```bash
python optimize.py               # Analyze performance
```

#### `update_whitelist.py`
Dynamic whitelist management for technical terms.
```bash
python update_whitelist.py force-update  # Force whitelist update
python update_whitelist.py stats         # Show statistics
python update_whitelist.py test --query "term"  # Test specific query
```

### **Domain Configuration**

#### `configure_domain.py`
Configure and switch between different business domains.
```bash
python configure_domain.py list                    # List available domains
python configure_domain.py current                 # Show current configuration
python configure_domain.py set FINANCE             # Set single domain
python configure_domain.py set AUTOMOTIVE FINANCE  # Set multi-domain
```

**Available Domains:**
- `AUTOMOTIVE` - Vehicle maintenance and repair
- `FINANCE` - Banking and financial services
- `HR` - Human resources management
- `IT_SUPPORT` - Technical IT support
- `HEALTHCARE` - Medical procedures and healthcare
- `LEGAL` - Legal information and compliance

### **Testing & Validation**

#### `system_tester.py`
Comprehensive system testing suite that validates all components.
```bash
python system_tester.py          # Run all system tests
```

**Tests Include:**
- Configuration validation
- MCP server initialization
- Document processing
- Security validations
- Query processing
- Session management
- Database integration

#### `validation_flow_demo.py`
Interactive demonstration of the validation system with practical examples.
```bash
python validation_flow_demo.py   # Run validation examples
```

**Features:**
- Real-time validation testing
- Interactive query examples
- Step-by-step validation flow demonstration
- Performance metrics

## 🚀 Usage Guidelines

### **Running Utilities**
All utilities should be run from the `utils/` directory:
```bash
cd utils
python <utility_name>.py [options]
```

### **Path Resolution**
Utilities automatically handle path resolution to the project root, so they can access all system components correctly.

### **Help Information**
Most utilities provide help information:
```bash
python <utility_name>.py --help
```

### **Prerequisites**
- Ensure the main system is properly configured (`.env` file)
- Database connection (for database-related utilities)
- Proper permissions for file operations

## 🔧 Development Notes

### **Adding New Utilities**
When adding new utilities to this directory:

1. **Path Handling**: Use this pattern for imports:
   ```python
   import sys
   from pathlib import Path
   
   # Add project root to path for imports
   sys.path.insert(0, str(Path(__file__).parent.parent))
   ```

2. **Error Handling**: Include comprehensive error handling
3. **Logging**: Use consistent logging format
4. **Documentation**: Update this README with the new utility

### **Utility Categories**
- **System Management**: Cache, database, performance monitoring
- **Configuration**: Domain setup, environment configuration
- **Testing**: System validation, integration tests
- **Development**: Debugging tools, development helpers

## 📊 Utility Dependencies

```
utils/
├── System Management
│   ├── cache_utils.py → cache_service.py
│   ├── monitor_database.py → database_logger.py
│   ├── optimize.py → config.py, performance modules
│   └── update_whitelist.py → security_utils.py
├── Domain Configuration
│   └── configure_domain.py → domain_config.py, domain_validation_config.py
└── Testing & Validation
    ├── system_tester.py → All system components
    └── validation_flow_demo.py → security_utils.py, validation system
```

## 🛡️ Security Considerations

- Utilities handle sensitive configuration data securely
- Database utilities use parameterized queries
- File operations include path traversal protection
- API keys are never logged or exposed

## 📝 Maintenance

### **Regular Tasks**
- Run `system_tester.py` after system updates
- Monitor database performance with `monitor_database.py`
- Update domain configurations as needed with `configure_domain.py`
- Optimize cache periodically with `cache_utils.py`

### **Troubleshooting**
- Check logs in `utils/logs/` for utility-specific issues
- Verify `.env` configuration for connection issues
- Ensure proper file permissions for file operations
- Check database connectivity for database utilities

## 🔗 Integration

These utilities integrate seamlessly with the main system:
- **Configuration**: Utilities respect system configuration
- **Logging**: Consistent with main system logging
- **Error Handling**: Compatible error handling patterns
- **Security**: Same security standards as main system

For more information about the main system, see the [main README](../README.md).
