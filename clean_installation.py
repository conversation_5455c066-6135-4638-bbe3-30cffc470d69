#!/usr/bin/env python3
"""
Installation Cleanup Utility for Softway Chatbot SYM

This script prepares a cloned installation for first use by cleaning up
all data from the previous installation while preserving the codebase.

Usage:
    python clean_installation.py [options]
    
Options:
    --dry-run       Show what would be cleaned without actually doing it
    --force         Skip confirmation prompts
    --keep-config   Keep existing .env file (useful for testing)
    --help          Show this help message
"""

import argparse
import asyncio
import logging
import os
import shutil
import sys
from pathlib import Path
from typing import List, Optional
import json
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import config
    from database_logger import get_database_logger, close_database_logger
except ImportError as e:
    print(f"Warning: Could not import some modules: {e}")
    print("Some cleanup operations may be skipped.")

class InstallationCleaner:
    """Handles cleaning of installation data for fresh deployments"""
    
    def __init__(self, dry_run: bool = False, force: bool = False, keep_config: bool = False):
        self.dry_run = dry_run
        self.force = force
        self.keep_config = keep_config
        self.cleaned_items = []
        self.errors = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def log_action(self, action: str, path: str = "", success: bool = True):
        """Log cleanup actions"""
        status = "✅" if success else "❌"
        message = f"{status} {action}"
        if path:
            message += f": {path}"
        
        if success:
            self.logger.info(message)
            self.cleaned_items.append(f"{action}: {path}" if path else action)
        else:
            self.logger.error(message)
            self.errors.append(f"{action}: {path}" if path else action)
        
        print(message)
    
    def confirm_action(self, message: str) -> bool:
        """Ask for user confirmation unless force mode is enabled"""
        if self.force:
            return True
        
        response = input(f"{message} (y/N): ").lower().strip()
        return response in ['y', 'yes']
    
    def clean_directory(self, path: Path, description: str, keep_structure: bool = True):
        """Clean a directory while optionally keeping its structure"""
        if not path.exists():
            self.log_action(f"Skip {description} (not found)", str(path))
            return
        
        if self.dry_run:
            self.log_action(f"Would clean {description}", str(path))
            return
        
        try:
            if keep_structure:
                # Remove contents but keep directory
                for item in path.iterdir():
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item)
            else:
                # Remove entire directory
                shutil.rmtree(path)
                path.mkdir(exist_ok=True)
            
            self.log_action(f"Cleaned {description}", str(path))
        except Exception as e:
            self.log_action(f"Failed to clean {description}: {e}", str(path), False)
    
    def clean_cache(self):
        """Clean application cache"""
        print("\n🧹 Cleaning Cache...")
        
        try:
            cache_path = config.cache_path
            self.clean_directory(cache_path, "cache directory")
        except Exception as e:
            self.log_action(f"Could not access cache path: {e}", "", False)
    
    def clean_logs(self):
        """Clean log files"""
        print("\n📝 Cleaning Logs...")
        
        try:
            log_path = config.log_path
            if log_path.exists():
                for log_file in log_path.glob("*.log*"):
                    if self.dry_run:
                        self.log_action("Would remove log file", str(log_file))
                    else:
                        try:
                            log_file.unlink()
                            self.log_action("Removed log file", str(log_file))
                        except Exception as e:
                            self.log_action(f"Failed to remove log file: {e}", str(log_file), False)
        except Exception as e:
            self.log_action(f"Could not access log path: {e}", "", False)
    
    def clean_temp_files(self):
        """Clean temporary files and uploads"""
        print("\n🗂️ Cleaning Temporary Files...")
        
        try:
            # Clean temp directory
            temp_path = config.temp_path
            self.clean_directory(temp_path, "temp directory")
            
            # Clean uploads directory
            upload_path = config.upload_path
            self.clean_directory(upload_path, "uploads directory")
            
        except Exception as e:
            self.log_action(f"Could not access temp/upload paths: {e}", "", False)
    
    async def clean_database(self):
        """Clean database tables (sessions, logs, etc.)"""
        print("\n🗄️ Cleaning Database...")
        
        if self.dry_run:
            self.log_action("Would clean database tables")
            return
        
        try:
            db_logger = await get_database_logger()
            
            # Clean chatbot logs
            if self.confirm_action("Clean all conversation logs from database?"):
                # This would require implementing a cleanup method in database_logger
                self.log_action("Database cleanup not yet implemented - manual cleanup required")
            
            await close_database_logger()
            
        except Exception as e:
            self.log_action(f"Database cleanup failed: {e}", "", False)
    
    def clean_sessions(self):
        """Clean session files"""
        print("\n🔐 Cleaning Sessions...")
        
        # Look for common session storage locations
        session_paths = [
            Path("sessions"),
            Path("storage/sessions"),
            Path("tmp/sessions")
        ]
        
        for session_path in session_paths:
            if session_path.exists():
                self.clean_directory(session_path, f"sessions in {session_path}")
    
    def clean_whitelist_cache(self):
        """Clean whitelist cache files"""
        print("\n📋 Cleaning Whitelist Cache...")
        
        whitelist_files = [
            "document_whitelist_cache.json",
            "whitelist_cache.json"
        ]
        
        for filename in whitelist_files:
            file_path = Path(filename)
            if file_path.exists():
                if self.dry_run:
                    self.log_action("Would remove whitelist cache", str(file_path))
                else:
                    try:
                        file_path.unlink()
                        self.log_action("Removed whitelist cache", str(file_path))
                    except Exception as e:
                        self.log_action(f"Failed to remove whitelist cache: {e}", str(file_path), False)
    
    def backup_config(self):
        """Create backup of current .env file"""
        env_file = Path(".env")
        if env_file.exists() and not self.keep_config:
            backup_name = f".env.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_path = Path(backup_name)
            
            if self.dry_run:
                self.log_action("Would backup .env file", backup_name)
            else:
                try:
                    shutil.copy2(env_file, backup_path)
                    self.log_action("Backed up .env file", backup_name)
                except Exception as e:
                    self.log_action(f"Failed to backup .env file: {e}", "", False)
    
    def create_fresh_env(self):
        """Create fresh .env file from template"""
        if self.keep_config:
            self.log_action("Keeping existing .env file")
            return
        
        env_example = Path(".env.example")
        env_file = Path(".env")
        
        if not env_example.exists():
            self.log_action("No .env.example found - cannot create fresh .env", "", False)
            return
        
        if self.dry_run:
            self.log_action("Would create fresh .env from template")
            return
        
        try:
            shutil.copy2(env_example, env_file)
            self.log_action("Created fresh .env from template")
            print("\n⚠️  IMPORTANT: Edit .env file with your specific configuration!")
        except Exception as e:
            self.log_action(f"Failed to create fresh .env: {e}", "", False)
    
    async def run_cleanup(self):
        """Run the complete cleanup process"""
        print("🚀 Softway Chatbot SYM - Installation Cleaner")
        print("=" * 60)
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No changes will be made")
        
        print(f"Installation ID: {getattr(config, 'installation_id', 'default')}")
        print(f"Target paths will be: cache_{config.installation_id}, logs_{config.installation_id}, etc.")
        
        if not self.force and not self.confirm_action("\nProceed with cleanup?"):
            print("Cleanup cancelled.")
            return
        
        # Backup current config
        self.backup_config()
        
        # Clean various components
        self.clean_cache()
        self.clean_logs()
        self.clean_temp_files()
        self.clean_sessions()
        self.clean_whitelist_cache()
        await self.clean_database()
        
        # Create fresh config
        self.create_fresh_env()
        
        # Summary
        print("\n" + "=" * 60)
        print("🎉 Cleanup Summary")
        print("=" * 60)
        print(f"✅ Successfully cleaned: {len(self.cleaned_items)} items")
        if self.errors:
            print(f"❌ Errors encountered: {len(self.errors)} items")
            for error in self.errors:
                print(f"   - {error}")
        
        if not self.dry_run:
            print("\n🔧 Next Steps:")
            print("1. Edit .env file with your installation-specific settings")
            print("2. Set unique INSTALLATION_ID")
            print("3. Configure database name, ports, etc.")
            print("4. Run: python setup.py")
            print("5. Run: python setup_database.py")

def main():
    parser = argparse.ArgumentParser(
        description="Clean installation for fresh deployment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be cleaned without actually doing it'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='Skip confirmation prompts'
    )
    
    parser.add_argument(
        '--keep-config',
        action='store_true',
        help='Keep existing .env file'
    )
    
    args = parser.parse_args()
    
    cleaner = InstallationCleaner(
        dry_run=args.dry_run,
        force=args.force,
        keep_config=args.keep_config
    )
    
    asyncio.run(cleaner.run_cleanup())

if __name__ == "__main__":
    main()
