"""
Cache Invalidation System
Monitors file changes and automatically invalidates cache entries
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Set, Optional, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent, FileDeletedEvent, FileMovedEvent

from cache_manager import DocumentCacheManager

logger = logging.getLogger(__name__)

class DocumentChangeHandler(FileSystemEventHandler):
    """Handles file system events for document cache invalidation"""
    
    def __init__(self, cache_manager: DocumentCacheManager, callback: Optional[Callable] = None):
        super().__init__()
        self.cache_manager = cache_manager
        self.callback = callback
        self.pending_invalidations: Set[Path] = set()
        self.last_invalidation_time: Dict[Path, float] = {}
        self.debounce_seconds = 2.0  # Debounce multiple events for same file
    
    def _should_process_file(self, file_path: Path) -> bool:
        """Check if file should be processed for cache invalidation"""
        if not file_path.exists():
            return True  # File was deleted, should invalidate
        
        # Only process PDF files
        if file_path.suffix.lower() != '.pdf':
            return False
        
        # Check if file is in a monitored directory structure
        parts = file_path.parts
        if 'sorgenti' not in parts:
            return False
        
        return True
    
    def _debounce_invalidation(self, file_path: Path) -> bool:
        """Check if enough time has passed since last invalidation"""
        current_time = time.time()
        last_time = self.last_invalidation_time.get(file_path, 0)
        
        if current_time - last_time < self.debounce_seconds:
            return False
        
        self.last_invalidation_time[file_path] = current_time
        return True
    
    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        if self._should_process_file(file_path) and self._debounce_invalidation(file_path):
            logger.info(f"Document modified: {file_path}")
            self.pending_invalidations.add(file_path)
            
            if self.callback:
                asyncio.create_task(self.callback(file_path, 'modified'))
    
    def on_deleted(self, event):
        """Handle file deletion events"""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        if self._should_process_file(file_path):
            logger.info(f"Document deleted: {file_path}")
            self.pending_invalidations.add(file_path)
            
            if self.callback:
                asyncio.create_task(self.callback(file_path, 'deleted'))
    
    def on_moved(self, event):
        """Handle file move/rename events"""
        if event.is_directory:
            return
        
        old_path = Path(event.src_path)
        new_path = Path(event.dest_path)
        
        # Invalidate cache for old path
        if self._should_process_file(old_path):
            logger.info(f"Document moved from: {old_path}")
            self.pending_invalidations.add(old_path)
            
            if self.callback:
                asyncio.create_task(self.callback(old_path, 'moved_from'))
        
        # Handle new path if it's a PDF
        if self._should_process_file(new_path):
            logger.info(f"Document moved to: {new_path}")
            
            if self.callback:
                asyncio.create_task(self.callback(new_path, 'moved_to'))

class CacheInvalidator:
    """Manages cache invalidation based on file system changes"""
    
    def __init__(self, cache_manager: DocumentCacheManager, watch_paths: list[Path]):
        self.cache_manager = cache_manager
        self.watch_paths = watch_paths
        self.observer = Observer()
        self.handler = DocumentChangeHandler(cache_manager, self._handle_file_change)
        self.is_running = False
        self.invalidation_queue = asyncio.Queue()
        self.worker_task = None
    
    async def _handle_file_change(self, file_path: Path, event_type: str):
        """Handle file change events"""
        await self.invalidation_queue.put((file_path, event_type))
    
    async def _invalidation_worker(self):
        """Worker task to process invalidation queue"""
        while self.is_running:
            try:
                # Wait for invalidation request with timeout
                file_path, event_type = await asyncio.wait_for(
                    self.invalidation_queue.get(), 
                    timeout=1.0
                )
                
                logger.info(f"Processing cache invalidation for {file_path} ({event_type})")
                
                # Invalidate cache for the file
                await self.cache_manager.invalidate_file_cache(file_path)
                
                # Mark task as done
                self.invalidation_queue.task_done()
                
            except asyncio.TimeoutError:
                # No invalidation requests, continue
                continue
            except Exception as e:
                logger.error(f"Error processing cache invalidation: {e}")
    
    def start_monitoring(self):
        """Start monitoring file system changes"""
        if self.is_running:
            logger.warning("Cache invalidator is already running")
            return
        
        try:
            # Set up file system monitoring
            for watch_path in self.watch_paths:
                if watch_path.exists():
                    self.observer.schedule(self.handler, str(watch_path), recursive=True)
                    logger.info(f"Monitoring directory for changes: {watch_path}")
                else:
                    logger.warning(f"Watch path does not exist: {watch_path}")
            
            # Start observer
            self.observer.start()
            
            # Start invalidation worker
            self.is_running = True
            self.worker_task = asyncio.create_task(self._invalidation_worker())
            
            logger.info("Cache invalidator started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start cache invalidator: {e}")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop monitoring file system changes"""
        if not self.is_running:
            return
        
        try:
            # Stop observer
            self.observer.stop()
            self.observer.join(timeout=5.0)
            
            # Stop worker task
            self.is_running = False
            if self.worker_task:
                self.worker_task.cancel()
            
            logger.info("Cache invalidator stopped")
            
        except Exception as e:
            logger.error(f"Error stopping cache invalidator: {e}")
    
    async def manual_invalidate(self, file_path: Path):
        """Manually invalidate cache for a specific file"""
        logger.info(f"Manual cache invalidation for: {file_path}")
        await self.cache_manager.invalidate_file_cache(file_path)
    
    async def bulk_invalidate(self, file_paths: list[Path]):
        """Invalidate cache for multiple files"""
        logger.info(f"Bulk cache invalidation for {len(file_paths)} files")
        
        for file_path in file_paths:
            await self.cache_manager.invalidate_file_cache(file_path)
    
    def get_pending_invalidations(self) -> Set[Path]:
        """Get list of files pending invalidation"""
        return self.handler.pending_invalidations.copy()
    
    def clear_pending_invalidations(self):
        """Clear pending invalidations list"""
        self.handler.pending_invalidations.clear()

class ScheduledCacheCleanup:
    """Scheduled cache cleanup and maintenance"""
    
    def __init__(self, cache_manager: DocumentCacheManager, cleanup_interval_hours: int = 6):
        self.cache_manager = cache_manager
        self.cleanup_interval_hours = cleanup_interval_hours
        self.cleanup_task = None
        self.is_running = False
    
    async def _cleanup_worker(self):
        """Worker task for scheduled cache cleanup"""
        while self.is_running:
            try:
                logger.info("Starting scheduled cache cleanup")
                
                # Clean up expired entries
                await self.cache_manager.cleanup_expired()
                
                # Enforce size limits
                await self.cache_manager.enforce_size_limit()
                
                # Log cache statistics
                stats = await self.cache_manager.get_stats()
                logger.info(f"Cache stats: {stats.total_entries} entries, "
                           f"{stats.total_size_mb:.1f}MB, "
                           f"hit rate: {stats.hit_rate:.2%}")
                
                # Wait for next cleanup cycle
                await asyncio.sleep(self.cleanup_interval_hours * 3600)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error during scheduled cache cleanup: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    def start_cleanup(self):
        """Start scheduled cache cleanup"""
        if self.is_running:
            logger.warning("Scheduled cache cleanup is already running")
            return
        
        self.is_running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_worker())
        logger.info(f"Started scheduled cache cleanup (interval: {self.cleanup_interval_hours}h)")
    
    def stop_cleanup(self):
        """Stop scheduled cache cleanup"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        logger.info("Stopped scheduled cache cleanup")
    
    async def manual_cleanup(self):
        """Manually trigger cache cleanup"""
        logger.info("Manual cache cleanup triggered")
        await self.cache_manager.cleanup_expired()
        await self.cache_manager.enforce_size_limit()
        
        stats = await self.cache_manager.get_stats()
        logger.info(f"Cache cleanup completed: {stats.total_entries} entries, "
                   f"{stats.total_size_mb:.1f}MB")
