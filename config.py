"""
Configuration management for MCP-based technical assistance chatbot.
Loads and validates all environment variables with secure defaults.
"""

import os
import logging
from pathlib import Path
from typing import Optional, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ConfigError(Exception):
    """Configuration error exception"""
    pass

class Config:
    """Configuration class with validation and secure defaults"""

    def __init__(self):
        self._validate_required_vars()
        self._setup_logging()

    # =============================================================================
    # INSTALLATION CONFIGURATION
    # =============================================================================
    @property
    def installation_id(self) -> str:
        """Unique identifier for this installation"""
        return os.getenv('INSTALLATION_ID', 'default')

    @property
    def app_name(self) -> str:
        return os.getenv('APP_NAME', 'Softway Chatbot SYM')

    @property
    def app_version(self) -> str:
        return os.getenv('APP_VERSION', '1.0.0')

    @property
    def app_environment(self) -> str:
        return os.getenv('APP_ENVIRONMENT', 'production')

    # =============================================================================
    # API CONFIGURATION
    # =============================================================================
    @property
    def google_api_key(self) -> str:
        key = os.getenv('GOOGLE_API_KEY')
        if not key:
            raise ConfigError("GOOGLE_API_KEY not found in environment variables")
        return key

    @property
    def jina_api_key(self) -> str:
        key = os.getenv('JINA-API-KEY')
        if not key:
            raise ConfigError("JINA-API-KEY not found in environment variables")
        return key

    @property
    def jina_api_url(self) -> str:
        return os.getenv('JINA_API_URL', 'https://api.jina.ai/v1/embeddings')

    @property
    def gemini_model(self) -> str:
        return os.getenv('GEMINI_MODEL', 'gemini-2.0-flash-exp')

    # =============================================================================
    # NETWORK CONFIGURATION
    # =============================================================================
    @property
    def web_host(self) -> str:
        return os.getenv('WEB_HOST', '0.0.0.0')

    @property
    def web_port(self) -> int:
        return int(os.getenv('WEB_PORT', '8000'))

    @property
    def web_workers(self) -> int:
        return int(os.getenv('WEB_WORKERS', '1'))

    @property
    def mcp_server_host(self) -> str:
        return os.getenv('MCP_SERVER_HOST', 'localhost')

    @property
    def mcp_server_port(self) -> int:
        return int(os.getenv('MCP_SERVER_PORT', '8080'))

    @property
    def mcp_timeout(self) -> int:
        return int(os.getenv('MCP_TIMEOUT', '30'))

    # =============================================================================
    # PATHS CONFIGURATION
    # =============================================================================
    def _get_installation_path(self, base_path: str, default: str) -> Path:
        """Get path with installation ID prefix if needed"""
        path_str = os.getenv(base_path, default)
        path = Path(path_str)

        # If it's a relative path and we have a non-default installation ID,
        # prefix with installation ID to avoid conflicts
        if not path.is_absolute() and self.installation_id != 'default':
            path = Path(f"{path}_{self.installation_id}")

        return path

    @property
    def sorgenti_path(self) -> Path:
        path = self._get_installation_path('SORGENTI_PATH', './sorgenti')
        if not path.exists():
            raise ConfigError(f"Sorgenti path does not exist: {path}")
        return path

    @property
    def cache_path(self) -> Path:
        path = self._get_installation_path('CACHE_PATH', './cache')
        path.mkdir(exist_ok=True)
        return path

    @property
    def log_path(self) -> Path:
        path = self._get_installation_path('LOG_PATH', './logs')
        path.mkdir(exist_ok=True)
        return path

    @property
    def temp_path(self) -> Path:
        path = self._get_installation_path('TEMP_PATH', './temp')
        path.mkdir(exist_ok=True)
        return path

    @property
    def upload_path(self) -> Path:
        path = self._get_installation_path('UPLOAD_PATH', './uploads')
        path.mkdir(exist_ok=True)
        return path
    
    # Performance Settings
    @property
    def max_context_tokens(self) -> int:
        return int(os.getenv('MAX_CONTEXT_TOKENS', '8000'))
    
    @property
    def chunk_size(self) -> int:
        return int(os.getenv('CHUNK_SIZE', '1000'))
    
    @property
    def overlap_size(self) -> int:
        return int(os.getenv('OVERLAP_SIZE', '200'))
    
    @property
    def cache_ttl(self) -> int:
        return int(os.getenv('CACHE_TTL', '3600'))

    # Advanced Cache Configuration
    @property
    def cache_dir(self) -> Path:
        path = Path(os.getenv('CACHE_DIR', './cache'))
        path.mkdir(exist_ok=True)
        return path

    @property
    def cache_type(self) -> str:
        return os.getenv('CACHE_TYPE', 'filesystem')  # filesystem or redis

    @property
    def cache_max_size_mb(self) -> int:
        return int(os.getenv('CACHE_MAX_SIZE_MB', '500'))

    @property
    def cache_ttl_hours(self) -> int:
        return int(os.getenv('CACHE_TTL_HOURS', '24'))

    @property
    def cache_cleanup_interval_hours(self) -> int:
        return int(os.getenv('CACHE_CLEANUP_INTERVAL_HOURS', '6'))

    # Redis Cache Configuration (if using Redis)
    @property
    def redis_url(self) -> str:
        return os.getenv('REDIS_URL', 'redis://localhost:6379')

    @property
    def redis_db(self) -> int:
        return int(os.getenv('REDIS_DB', '0'))

    @property
    def redis_key_prefix(self) -> str:
        return os.getenv('REDIS_KEY_PREFIX', 'docache:')
    
    # Security Settings
    @property
    def max_file_size_mb(self) -> int:
        return int(os.getenv('MAX_FILE_SIZE_MB', '50'))
    
    @property
    def allowed_extensions(self) -> list:
        extensions = os.getenv('ALLOWED_EXTENSIONS', '.pdf')
        return [ext.strip() for ext in extensions.split(',')]
    
    @property
    def max_concurrent_docs(self) -> int:
        return int(os.getenv('MAX_CONCURRENT_DOCS', '10'))

    # Domain Configuration
    @property
    def system_scope(self) -> str:
        """Get single domain for backward compatibility"""
        scopes = self.system_scopes
        if not scopes:
            raise ValueError("No SYSTEM_SCOPE configured. Please set SYSTEM_SCOPE environment variable.")
        return scopes[0]

    @property
    def system_scopes(self) -> List[str]:
        """Get list of configured domains"""
        scope_str = os.getenv('SYSTEM_SCOPE', '').upper()
        if not scope_str:
            raise ValueError("SYSTEM_SCOPE environment variable is required but not set.")
        # Split by comma and clean up whitespace
        scopes = [scope.strip() for scope in scope_str.split(',') if scope.strip()]
        if not scopes:
            raise ValueError("SYSTEM_SCOPE environment variable is empty or invalid.")

        # Validate that all domains exist
        self._validate_domains(scopes)
        return scopes

    def _validate_domains(self, domains: List[str]) -> None:
        """Validate that all specified domains exist in domain configuration"""
        try:
            from domain_config import get_available_domains
            available_domains = get_available_domains()

            invalid_domains = [d for d in domains if d not in available_domains]
            if invalid_domains:
                raise ValueError(f"Invalid domains: {', '.join(invalid_domains)}. Available domains: {', '.join(available_domains)}")
        except ImportError:
            # If domain_config is not available, skip validation
            pass

    # =============================================================================
    # DATABASE CONFIGURATION
    # =============================================================================
    @property
    def db_host(self) -> str:
        return os.getenv('DB_HOST', 'localhost')

    @property
    def db_port(self) -> int:
        return int(os.getenv('DB_PORT', '3306'))

    @property
    def db_database(self) -> str:
        """Database name with installation ID suffix for multi-installation support"""
        base_db = os.getenv('DB_DATABASE', 'softway_chat')
        if self.installation_id != 'default':
            return f"{base_db}_{self.installation_id}"
        return base_db

    @property
    def db_user(self) -> str:
        return os.getenv('DB_USER', 'root')

    @property
    def db_password(self) -> str:
        return os.getenv('DB_PASSWORD', '')

    @property
    def db_charset(self) -> str:
        return os.getenv('DB_CHARSET', 'utf8mb4')

    @property
    def db_collation(self) -> str:
        return os.getenv('DB_COLLATION', 'utf8mb4_unicode_ci')

    @property
    def db_connection_timeout(self) -> int:
        return int(os.getenv('DB_CONNECTION_TIMEOUT', '30'))

    @property
    def db_pool_size(self) -> int:
        return int(os.getenv('DB_POOL_SIZE', '5'))

    # =============================================================================
    # REDIS CONFIGURATION WITH INSTALLATION SUPPORT
    # =============================================================================
    @property
    def redis_db_with_installation(self) -> int:
        """Redis DB with installation ID offset for multi-installation support"""
        base_db = int(os.getenv('REDIS_DB', '0'))
        if self.installation_id != 'default':
            # Use a hash of installation_id to get a consistent offset
            installation_hash = hash(self.installation_id) % 10
            return base_db + installation_hash
        return base_db

    @property
    def redis_key_prefix_with_installation(self) -> str:
        """Redis key prefix with installation ID for multi-installation support"""
        base_prefix = os.getenv('REDIS_KEY_PREFIX', 'docache:')
        if self.installation_id != 'default':
            return f"{base_prefix}{self.installation_id}:"
        return base_prefix

    # MCP Server Settings
    @property
    def mcp_server_host(self) -> str:
        return os.getenv('MCP_SERVER_HOST', 'localhost')
    
    @property
    def mcp_server_port(self) -> int:
        return int(os.getenv('MCP_SERVER_PORT', '8080'))
    
    @property
    def mcp_timeout(self) -> int:
        return int(os.getenv('MCP_TIMEOUT', '30'))
    
    # Gemini-specific Settings
    @property
    def gemini_temperature(self) -> float:
        return float(os.getenv('GEMINI_TEMPERATURE', '0.2'))
    
    @property
    def gemini_max_output_tokens(self) -> int:
        return int(os.getenv('GEMINI_MAX_OUTPUT_TOKENS', '2048'))
    
    @property
    def gemini_top_p(self) -> float:
        return float(os.getenv('GEMINI_TOP_P', '0.8'))
    
    @property
    def gemini_top_k(self) -> int:
        return int(os.getenv('GEMINI_TOP_K', '40'))
    
    # Logging Configuration
    @property
    def log_level(self) -> str:
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def log_format(self) -> str:
        return os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    @property
    def log_max_bytes(self) -> int:
        return int(os.getenv('LOG_MAX_BYTES', '10485760'))
    
    @property
    def log_backup_count(self) -> int:
        return int(os.getenv('LOG_BACKUP_COUNT', '5'))
    
    def _validate_required_vars(self):
        """Validate presence of required environment variables"""
        required_vars = ['GOOGLE_API_KEY', 'JINA-API-KEY']
        missing_vars = []

        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            raise ConfigError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_file = self.log_path / 'chatbot.log'
        
        # Create rotating file handler
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=self.log_max_bytes,
            backupCount=self.log_backup_count
        )
        
        # Create console handler
        console_handler = logging.StreamHandler()
        
        # Create formatter
        formatter = logging.Formatter(self.log_format)
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            handlers=[file_handler, console_handler]
        )
        
        # Suppress some verbose loggers
        logging.getLogger('httpx').setLevel(logging.WARNING)
        logging.getLogger('sentence_transformers').setLevel(logging.WARNING)

    # =============================================================================
    # SESSION CONFIGURATION
    # =============================================================================
    @property
    def session_lifetime(self) -> int:
        return int(os.getenv('SESSION_LIFETIME', '3600'))

    @property
    def session_cleanup_interval(self) -> int:
        return int(os.getenv('SESSION_CLEANUP_INTERVAL', '1800'))

    @property
    def session_storage(self) -> str:
        return os.getenv('SESSION_STORAGE', 'filesystem')

    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================
    @property
    def api_secret_key(self) -> str:
        key = os.getenv('API_SECRET_KEY')
        if not key:
            raise ConfigError("API_SECRET_KEY not found in environment variables")
        return key

    @property
    def allowed_hosts(self) -> List[str]:
        hosts = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1')
        return [host.strip() for host in hosts.split(',')]

    @property
    def cors_origins(self) -> List[str]:
        origins = os.getenv('CORS_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000')
        return [origin.strip() for origin in origins.split(',')]

    @property
    def rate_limit_requests(self) -> int:
        return int(os.getenv('RATE_LIMIT_REQUESTS', '100'))

    @property
    def rate_limit_window(self) -> int:
        return int(os.getenv('RATE_LIMIT_WINDOW', '3600'))

    # =============================================================================
    # CLEANUP CONFIGURATION
    # =============================================================================
    @property
    def auto_cleanup_enabled(self) -> bool:
        return os.getenv('AUTO_CLEANUP_ENABLED', 'true').lower() == 'true'

    @property
    def cleanup_schedule(self) -> str:
        return os.getenv('CLEANUP_SCHEDULE', '0 2 * * *')

    @property
    def cleanup_retention_days(self) -> int:
        return int(os.getenv('CLEANUP_RETENTION_DAYS', '30'))

    @property
    def cleanup_logs(self) -> bool:
        return os.getenv('CLEANUP_LOGS', 'true').lower() == 'true'

    @property
    def cleanup_cache(self) -> bool:
        return os.getenv('CLEANUP_CACHE', 'true').lower() == 'true'

    @property
    def cleanup_sessions(self) -> bool:
        return os.getenv('CLEANUP_SESSIONS', 'true').lower() == 'true'

    @property
    def cleanup_temp_files(self) -> bool:
        return os.getenv('CLEANUP_TEMP_FILES', 'true').lower() == 'true'

# Global config instance
config = Config()
