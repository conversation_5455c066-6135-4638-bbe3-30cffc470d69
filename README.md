# Softway Chatbot SYM - Technical Assistance System

A sophisticated multi-product technical assistance chatbot that uses the Model Context Protocol (MCP) approach with Google Gemini-2.0-Flash for intelligent document processing and query answering.

## 🚀 Features

- **MCP Architecture**: Uses Model Context Protocol for efficient document management
- **Multi-Product Support**: Handle multiple product lines with separate documentation
- **Smart Citations**: Automatic citation management based on document categories
- **Semantic Search**: Advanced semantic search using sentence transformers
- **Conversation Continuity**: Maintains context across multiple queries
- **Security First**: Comprehensive input validation and rate limiting
- **Rich CLI Interface**: Beautiful command-line interface with Rich library
- **Web Interface**: Modern, responsive web interface with real-time chat
- **Database Logging**: Complete conversation logging with analytics
- **Dynamic Whitelist**: Automatic extraction of technical terms from documents
- **Visual Features**: Advanced UI with markdown support and progress indicators
- **Multi-Domain Support**: Adapt the chatbot to different business domains like Automotive, Finance, HR, etc.
- **Multi-Installation Support**: Run multiple, isolated instances of the chatbot on the same server.

## ⚡ Quick Start

### 1. Installation
```bash
# Install dependencies
python setup.py

# Configure API keys in .env file
cp .env.example .env
# Edit .env with your API keys
```

### 2. Usage Options

#### CLI Interface
```bash
python main.py run
```

#### Web Interface
```bash
python start_web.py
# Open http://localhost:8000
```

#### System Tests
```bash
python test_system.py
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Google AI Studio API key (for Gemini-2.0-Flash)
- MySQL Server (optional, for logging)
- PDF documents in the specified directory structure

### Automatic Setup
```bash
python setup.py
```

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Setup database (optional)
python setup_database.py

# Configure environment
cp .env.example .env
# Edit .env with your settings
```

### Document Structure
```
sorgenti/
├── SYM/
│   ├── link/     # Documents requiring citations
│   │   └── *.pdf
│   └── nolink/   # Documents without citations
│       └── *.pdf
└── [OTHER_PRODUCTS]/
    ├── link/
    └── nolink/
```

## 🔧 Configuration

The system is fully configurable through the `.env` file, supporting multiple installations on the same server.

### Key Configuration Options

```env
# =============================================================================
# INSTALLATION CONFIGURATION
# =============================================================================
# Unique identifier for this installation (REQUIRED for multiple installations)
INSTALLATION_ID=default

# Application settings
APP_NAME=Softway Chatbot SYM
APP_VERSION=1.0.0
APP_ENVIRONMENT=production

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# Web server settings (each installation needs different ports)
WEB_HOST=0.0.0.0
WEB_PORT=8000

# =============================================================================
# API CONFIGURATION
# =============================================================================
GOOGLE_API_KEY=your_api_key_here
GEMINI_MODEL=gemini-2.0-flash-exp
JINA-API-KEY=your_jina_api_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database will be automatically prefixed with INSTALLATION_ID
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=softway_chat
DB_USER=prova
DB_PASSWORD=prova

# =============================================================================
# PATHS CONFIGURATION
# =============================================================================
# Paths are automatically prefixed with INSTALLATION_ID for isolation
SORGENTI_PATH=./sorgenti
CACHE_PATH=./cache
LOG_PATH=./logs

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200
CACHE_TTL=3600

# Security Settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=.pdf
MAX_CONCURRENT_DOCS=10

# Domain Configuration
SYSTEM_SCOPE=AUTOMOTIVE
```

**Note:** API keys are required for Gemini and Jina services. Never share your keys publicly.

## 🔄 Multiple Installations Support

The system supports running multiple installations on the same server without conflicts. Each installation is identified by a unique `INSTALLATION_ID` and has its own database, cache, logs, and port.

### Quick Multi-Installation Setup

```bash
# Create a new installation
python manage_installations.py create staging --port 8001

# List all installations
python manage_installations.py list

# Check status of all installations
python manage_installations.py status

# Remove an installation
python manage_installations.py remove staging
```

### Manual Multi-Installation Setup

For detailed step-by-step instructions, see the [Cloning Guide](#-cloning-guide).

**Example configurations for multiple installations:**

```env
# Production Installation
INSTALLATION_ID=prod
WEB_PORT=8000
DB_DATABASE=softway_chat_prod

# Staging Installation
INSTALLATION_ID=staging
WEB_PORT=8001
DB_DATABASE=softway_chat_staging

# Development Installation
INSTALLATION_ID=dev
WEB_PORT=8002
DB_DATABASE=softway_chat_dev
```

### Installation Cleanup

When cloning an installation, use the cleanup utility:

```bash
# Clean installation for fresh deployment
python clean_installation.py

# Dry run to see what would be cleaned
python clean_installation.py --dry-run

# Force cleanup without prompts
python clean_installation.py --force
```

## 🎯 Domain Configuration (SYSTEM_SCOPE)

The system can be configured to operate in different business domains by setting the `SYSTEM_SCOPE` environment variable. This allows the AI to adapt its knowledge scope, terminology, and validation rules to specific sectors.

### Available Domains

| Domain | Code | Description |
|--------|------|-------------|
| **Automotive** | `AUTOMOTIVE` | Vehicle maintenance, repair, and technical assistance |
| **Finance** | `FINANCE` | Banking, investments, and financial services |
| **Human Resources** | `HR` | Personnel management, recruitment, and HR procedures |
| **IT Support** | `IT_SUPPORT` | Technical support, troubleshooting, and IT assistance |
| **Healthcare** | `HEALTHCARE` | Medical procedures and healthcare information |
| **Legal** | `LEGAL` | Legal information and compliance guidance |

### Quick Domain Switch

```bash
# Single domain
python switch_domain.py --switch FINANCE

# Multiple domains
python switch_domain.py --switch "AUTOMOTIVE,FINANCE"

# Check current configuration
python switch_domain.py --current

# Manual .env editing
echo "SYSTEM_SCOPE=AUTOMOTIVE,IT_SUPPORT" >> .env
```

### Domain-Specific Features

Each domain includes:
- **Specialized Keywords**: Domain-specific terminology validation
- **Custom Prompts**: Tailored system instructions for the AI
- **Rejection Messages**: Contextual guidance when queries are outside scope
- **Technical Patterns**: Domain-specific validation rules

### Multi-Domain Support

The system supports **multiple domains simultaneously**:
- **Combined Keywords**: Union of all configured domains
- **Intelligent Validation**: Accepts queries from any active domain
- **Adaptive Prompts**: AI automatically identifies and responds in appropriate domain context
- **Flexible Configuration**: `SYSTEM_SCOPE=AUTOMOTIVE,FINANCE,HR`

📖 **For detailed configuration guide, see the [System Scope Guide](#-system-scope-guide).**

## 💬 Usage Examples

### Domain-Specific Examples

#### Automotive Domain (SYSTEM_SCOPE=AUTOMOTIVE)
```
Sistema: Prodotto SYM caricato. Come posso aiutarti?

Utente: Come avvito i bulloni della testa?
🤖 Assistant: I bulloni della testa vanno serrati con coppia di 1.8~2.2 Kg-m.

📚 Sources:
• Manuale officina Symphony ST 200 E5.pdf, pagina 4

Utente: E i prigionieri?
🤖 Assistant: I prigionieri testa/cilindro vanno serrati con coppia di 0.7~1.1 Kg-m
e devono essere avvitati al carter motore.

📚 Sources:
• Manuale officina Symphony ST 200 E5.pdf, pagina 4
```

#### Finance Domain (SYSTEM_SCOPE=FINANCE)
```
Sistema: Sistema finanziario attivo. Come posso aiutarti?

Utente: Come funziona un mutuo a tasso variabile?
🤖 Assistant: Un mutuo a tasso variabile ha un tasso di interesse che cambia
periodicamente in base all'andamento di un parametro di riferimento come l'Euribor...

📚 Sources:
• Guida prodotti bancari.pdf, pagina 12
```

#### IT Support Domain (SYSTEM_SCOPE=IT_SUPPORT)
```
Sistema: Supporto IT attivo. Come posso aiutarti?

Utente: Come configuro una VPN aziendale?
🤖 Assistant: Per configurare una VPN aziendale, segui questi passaggi:
1. Accedi alle impostazioni di rete...
2. Seleziona "Aggiungi connessione VPN"...

📚 Sources:
• Manuale configurazione rete.pdf, pagina 8
```

#### Multi-Domain Example (SYSTEM_SCOPE=AUTOMOTIVE,FINANCE)
```
Sistema: Sistema multi-dominio attivo (Automotive + Finance). Come posso aiutarti?

Utente: Come cambio l'olio del motore della mia auto?
🤖 Assistant: Per il cambio olio del motore, segui questa procedura:
1. Scalda il motore per alcuni minuti...
2. Posiziona il contenitore sotto il tappo di scarico...

📚 Sources:
• Manuale manutenzione auto.pdf, pagina 15

Utente: Quanto costa un mutuo per 200.000 euro?
🤖 Assistant: Il costo di un mutuo dipende da diversi fattori:
- Tasso di interesse (fisso o variabile)
- Durata del finanziamento...
- Spread applicato dalla banca...

📚 Sources:
• Guida prodotti bancari.pdf, pagina 23
```

## 🌐 Web Interface Features

### Visual Enhancements
- **Thinking Indicators**: Animated progress messages during processing
- **Markdown Support**: Rich text formatting (bold, italic, lists)
- **Source Management**: Interactive source citations with detailed modal
- **Real-time Updates**: WebSocket-based instant communication
- **Responsive Design**: Optimized for desktop and mobile

### Mobile Fixes
The web interface is optimized for mobile devices to prevent common issues like the input bar disappearing behind the virtual keyboard. This is achieved through:
- **Dynamic Viewport Height (`100dvh`)**: Ensures the chat container resizes correctly when the browser's UI appears or disappears.
- **Sticky Footer**: The input bar is always visible at the bottom of the screen.
- **Safe Area Support**: The layout adapts to devices with notches (e.g., iPhone X).
- **Virtual Keyboard Handling**: The view automatically scrolls to keep the input field visible when the keyboard is open.
- **Zoom Prevention**: The font size of the input field is set to `16px` to prevent automatic zooming on iOS.

### Available Endpoints
- `GET /` - Web interface
- `GET /api/health` - Health check
- `GET /api/products` - Available products
- `POST /api/session/create` - Create new session
- `WS /ws/{session_id}` - WebSocket chat connection

## 🗄️ Database Integration

### Automatic Logging
- **Complete Tracking**: All conversations (CLI + Web) automatically logged
- **Performance Metrics**: Response time, confidence, search results
- **User Analytics**: IP addresses, session tracking, interface type
- **Tool Execution**: Detailed logging of MCP tool usage

### Database Schema
```sql
-- Main logging table
CREATE TABLE chatbot_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    product VARCHAR(100),
    interface_type ENUM('CLI','WEB') DEFAULT 'CLI',
    question TEXT NOT NULL,
    answer MEDIUMTEXT,
    search_result_count INT DEFAULT 0,
    avg_relevance_score DECIMAL(5,4),
    used_general_knowledge BOOLEAN DEFAULT FALSE,
    response_time_ms INT,
    confidence DECIMAL(5,4),
    tool_executions JSON
);
```

### Analytics Queries
```sql
-- Performance by product
SELECT product, AVG(response_time_ms), COUNT(*)
FROM chatbot_logs
GROUP BY product;

-- CLI vs Web comparison
SELECT interface_type, AVG(confidence), AVG(response_time_ms)
FROM chatbot_logs
GROUP BY interface_type;
```

## 🔍 Dynamic Whitelist System

### Domain-Aware Term Extraction
The system automatically extracts domain-specific terms from PDF documents to improve query validation. The extraction adapts to the configured `SYSTEM_SCOPE`:

#### Automotive Domain Examples:
- **Brand Names**: Silkolene, Castrol, Motul, Synerject
- **Technical Terms**: "corpo farfallato", "sistema Synerject", "lubrificanti Silkolene"
- **Model Codes**: ST200, M4B, E5+
- **Diagnostic Codes**: P0116, P0420, C1032

#### Finance Domain Examples:
- **Product Names**: "Conto Corrente Premium", "Mutuo Casa"
- **Technical Terms**: "tasso fisso", "spread bancario", "TAEG"
- **Codes**: IBAN, BIC, ABI/CAB

#### IT Support Domain Examples:
- **Software Names**: "Windows Server 2022", "VMware vSphere"
- **Technical Terms**: "Active Directory", "DNS resolution"
- **Error Codes**: 0x80070005, HTTP 404

### Whitelist Management
```bash
# Update whitelist manually
python update_whitelist.py force-update

# Show statistics
python update_whitelist.py stats

# Test specific query
python update_whitelist.py test --query "Lubrificanti Silkolene"
```

### Benefits
- **Domain Adaptation**: Automatically adapts to different business sectors
- **Reduced False Negatives**: Terms in documents are no longer blocked
- **Automatic Updates**: Whitelist stays synchronized with documents
- **Cross-Domain Flexibility**: Supports new domains and technical terms automatically
- **Contextual Validation**: Combines static keywords with dynamic document terms

## 🛡️ Validation Flow

The system uses a 4-step conditional flow to validate user requests:

1.  **Blacklist Filter**: Blocks inappropriate terms.
2.  **Document Query**: Performs a semantic search in the knowledge base. If relevant results are found, the request is approved.
3.  **Whitelist Analysis**: Checks for domain-specific technical terms. If found, the request is approved.
4.  **Failure Condition**: If none of the above conditions are met, the request is blocked.

This ensures that only relevant and appropriate queries are processed by the AI.

## 🏗️ Architecture

### MCP (Model Context Protocol) Approach
The system uses MCP instead of traditional RAG for several advantages:
- **Dynamic Loading**: Documents are loaded on-demand
- **Efficient Context Management**: Better memory usage
- **Real-time Processing**: No pre-indexing required
- **Flexible Resource Management**: Easy to add/remove documents

### Core Components
1. **MCP Server**: Manages resources, tools, and context
2. **Document Processor**: Extracts and indexes PDF content
3. **Query Engine**: Processes queries using MCP tools
4. **Session Manager**: Handles user sessions and continuity
5. **Security Layer**: Validates inputs and manages rate limits
6. **Database Logger**: Records all conversations and metrics
7. **Web Server**: FastAPI-based web interface

### Citation Logic
- **`link/` documents**: Automatically include citations with filename and page
- **`nolink/` documents**: No citations added to responses
- **Smart Detection**: System automatically determines citation requirements

## 📁 Project Structure

```
softway-chatbot-sym/
├── main.py                    # CLI entry point
├── start_web.py              # Web interface launcher
├── config.py                 # Configuration management
├── mcp_server.py             # MCP server core
├── mcp_tools.py              # MCP tools implementation
├── document_processor.py      # PDF processing and indexing
├── query_engine.py           # Query processing pipeline
├── session_manager.py        # Session and conversation management
├── security_utils.py         # Security and error handling
├── database_logger.py        # Database logging system
├── web_server.py             # FastAPI web server
├── setup.py                  # Installation setup script
├── setup_database.py         # Database setup script
├── requirements.txt          # Python dependencies
├── .env.example              # Environment template
├── frontend/templates/       # Web interface files
│   ├── index.html           # Main web interface
│   ├── style.css            # CSS styles
│   └── script.js            # JavaScript functionality
├── database/                 # Database schema and migrations
│   ├── create_chatbot_logs_table.sql
│   ├── migrate_add_interface_type.sql
│   └── test_chatbot_logs.sql
├── sorgenti/                 # Document storage
│   └── [PRODUCT_NAME]/
│       ├── link/            # Documents with citations
│       └── nolink/          # Documents without citations
├── cache/                    # Document cache
├── logs/                     # Application logs
└── test_*.py                # Test files
```

## 🧪 Testing

### Comprehensive Test Suite
```bash
# System tests
python test_system.py

# Web interface tests
python test_web_integration.py

# Database integration tests
python test_database_integration.py

# Visual features tests
python test_visual_features.py

# Whitelist system tests
python test_final_whitelist.py

# Interface type tests
python test_interface_type.py
```

### Test Coverage
- Configuration loading and validation
- MCP server initialization and tools
- Document processing and indexing
- Security validations and rate limiting
- Query processing and response generation
- Citation logic and source management
- Session management and continuity
- Database logging and analytics
- Web interface functionality
- Whitelist system operation

## 📊 Performance & Monitoring

### Key Metrics
- **Response Time**: < 5 seconds for standard queries
- **Memory Usage**: Efficient caching and cleanup
- **Scalability**: Handles multiple concurrent sessions
- **Context Management**: Maintains conversation history
- **Database Performance**: Optimized queries with proper indexing

### Monitoring Tools
```bash
# Real-time database monitoring
python monitor_database.py

# Performance testing
python test_cache_performance.py

# System diagnostics
python test_diagnostic_codes.py
```

### Analytics Available
- Conversation volume and patterns
- Response time analysis
- Confidence score tracking
- Tool usage statistics
- Interface type comparison (CLI vs Web)
- Product-specific metrics

## 🛠️ Utilities

The `utils/` directory contains essential scripts for system management and testing:

### **System Management**
- **Cache Management (`cache_utils.py`)**: View stats, clear, and optimize the cache.
- **Database Monitoring (`monitor_database.py`)**: Real-time monitoring of conversation logs.
- **Performance Analysis (`optimize.py`)**: Analyze system performance and get optimization recommendations.
- **Whitelist Management (`update_whitelist.py`)**: Manage the dynamic whitelist of technical terms.

### **Domain Configuration**
- **Domain Configuration (`configure_domain.py`)**: Configure and switch between different business domains.
  ```bash
  # Usage examples
  cd utils
  python configure_domain.py list                    # List available domains
  python configure_domain.py current                 # Show current configuration
  python configure_domain.py set FINANCE             # Set single domain
  python configure_domain.py set AUTOMOTIVE FINANCE  # Set multi-domain
  ```

### **Testing & Validation**
- **System Tester (`system_tester.py`)**: Comprehensive system testing suite.
  ```bash
  cd utils
  python system_tester.py  # Run all system tests
  ```
- **Validation Flow Demo (`validation_flow_demo.py`)**: Interactive demonstration of the validation system.
  ```bash
  cd utils
  python validation_flow_demo.py  # Run validation examples
  ```

### **Usage Notes**
- All utilities are designed to be run from the `utils/` directory
- They automatically handle path resolution to the project root
- Most utilities provide `--help` option for detailed usage information

## 🔒 Security Features

- **Input Validation**: Comprehensive query and file path validation
- **Rate Limiting**: API and user request rate limiting
- **Safe Logging**: Sensitive data is never logged
- **Path Traversal Protection**: Prevents unauthorized file access
- **XSS Prevention**: Filters potentially dangerous content
- **Dynamic Whitelist**: Automatic validation of technical terms
- **SQL Injection Protection**: Parameterized database queries
- **API Key Security**: Environment-based key management

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**:
   - Ensure your Google AI Studio API key is correctly set in `.env`
   - Check that the API key has proper permissions

2. **No Documents Found**:
   - Verify PDF files are in the correct directory structure
   - Check file permissions and extensions

3. **Database Connection Issues**:
   - Verify MySQL server is running
   - Check database credentials in `.env`
   - Run `python setup_database.py` to initialize

4. **Web Interface Not Loading**:
   - Check that port 8000 is available
   - Verify static files are in `frontend/templates/`
   - Check browser console for JavaScript errors

5. **Memory Issues**:
   - Reduce `MAX_CONCURRENT_DOCS` in `.env`
   - Clear cache directory periodically
   - Monitor with `python monitor_database.py`

### Debug Mode
Enable debug logging by setting in `.env`:
```env
LOG_LEVEL=DEBUG
```

## ❓ FAQ

**Q: How do I add a new product?**
A: Create a new folder in `sorgenti/` following the `link/` and `nolink/` structure.

**Q: How do I update dependencies?**
A: Modify `requirements.txt` and run `python setup.py`.

**Q: Can I use this without a database?**
A: Yes, the system works without database logging, but you'll lose analytics features.

**Q: How do I backup conversations?**
A: Use the web interface export feature or query the database directly.

**Q: How do I customize the web interface?**
A: Modify files in `frontend/templates/` - `style.css` for styling, `script.js` for functionality.

## 🤝 Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include security validations
4. Write tests for new features
5. Update documentation
6. Open a pull request with clear description of changes

## 📚 Appendix

### 🔄 Cloning Guide

This guide explains how to duplicate an existing installation of the **Softway Chatbot SYM** on the same server without causing conflicts.

#### 📋 Prerequisites

- SSH access to the server
- A working existing installation (`installation-A`)
- Permissions to create new directories and databases
- MySQL/MariaDB with privileges to create new databases

#### 🚀 Step-by-Step Procedure

1.  **Clone the Code**:
    ```bash
    # Navigate to the parent directory of the installations
    cd /path/to/installations

    # Clone the existing installation
    cp -r installation-A installation-B

    # Enter the new installation directory
    cd installation-B
    ```

2.  **Clean the Installation**:
    Use the automatic cleaning script to remove all data from the previous installation:
    ```bash
    # Perform a dry run to see what will be done
    python clean_installation.py --dry-run

    # Perform the actual cleaning
    python clean_installation.py --force

    # Or run an interactive cleaning (with confirmations)
    python clean_installation.py
    ```
    The script will automatically clean:
    - ✅ Application cache
    - ✅ Log files
    - ✅ Temporary files and uploads
    - ✅ Whitelist cache
    - ✅ Active sessions
    - ✅ Backup of the existing .env file
    - ✅ Creation of a new .env from the template

3.  **Configure the New Installation**:
    Edit the `.env` file with the specific settings for the new installation:
    ```bash
    nano .env
    ```
    **MANDATORY configurations to change:**
    ```env
    # Unique identifier for this installation
    INSTALLATION_ID=installation-b

    # Different web port to avoid conflicts
    WEB_PORT=8001

    # Separate database
    DB_DATABASE=softway_chat_installation_b
    ```

4.  **Set up the Database**:
    Create the database for the new installation:
    ```bash
    # Automatic method (recommended)
    python setup_database.py
    ```

5.  **Install Dependencies**:
    ```bash
    # Install/update Python dependencies
    python setup.py
    ```

6.  **Test the New Installation**:
    ```bash
    # Test configuration
    python -c "from config import config; print(f'Installation ID: {config.installation_id}')"

    # Start the web interface
    python start_web.py
    ```

### 📖 System Scope Guide

This guide explains how to configure the `SYSTEM_SCOPE` to adapt the chatbot to different application domains.

#### Basic Configuration

1.  **.env File**:
    Add or modify the following variable in your `.env` file:
    ```env
    # Single Domain
    SYSTEM_SCOPE=AUTOMOTIVE

    # Multiple Domains
    SYSTEM_SCOPE=AUTOMOTIVE,FINANCE,HR
    ```

2.  **Available Domains**:
    - `AUTOMOTIVE`: Technical assistance for motor vehicles.
    - `FINANCE`: Financial, banking, and insurance services.
    - `HR`: Human resources and personnel management.
    - `IT_SUPPORT`: Technical IT support.
    - `HEALTHCARE`: Health information and medical procedures.
    - `LEGAL`: Legal information and regulatory compliance.

#### Multi-Domain Configurations

- **Extended Expertise**: The system can answer questions from multiple sectors.
- **Combined Terminology**: Keywords from all active domains are used.
- **Intelligent Validation**: Accepts queries related to any configured domain.
- **Adaptive Prompts**: The AI automatically adapts to the type of question.

#### Technical Implementation

- **Dynamic Whitelist**: The system maintains the dynamic whitelist functionality that automatically extracts terms from the documentation. This works in conjunction with the keywords of the configured domain.
- **Query Validation**: Queries are validated against domain keywords, non-keywords, the document whitelist, and technical patterns.
- **Custom Rejection Messages**: Each domain has a specific rejection message to guide the user towards appropriate questions.

## 📬 Contact & Support

For bug reports, feature requests, or support:
- **Issue Tracker**: [GitHub Issues](https://github.com/mbiagiottime/softway-chatbot-sym/issues)
- **Email**: <EMAIL>

## 📄 License

This project is provided as-is for educational and internal use purposes.

## 🙏 Acknowledgments

- Google AI Studio for Gemini API
- Sentence Transformers for semantic search
- Rich library for beautiful CLI interface
- PyMuPDF for PDF processing
- FastAPI for web framework
- MySQL for database functionality
