#!/usr/bin/env python3
"""
Multi-Domain Manager - Gestione intelligente delle keywords multi-dominio
Risolve i conflitti tra domini con terminologie sovrapposte
"""

import logging
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class DomainInfo:
    """Informazioni su un dominio specifico"""
    name: str
    keywords: Set[str]
    non_keywords: Set[str]
    priority: int = 0  # Priorità per risolvere conflitti
    description: Optional[str] = None


@dataclass
class KeywordConflict:
    """Rappresenta un conflitto tra domini per una keyword"""
    keyword: str
    domains_with_keyword: List[str]
    domains_with_non_keyword: List[str]
    resolution: Optional[str] = None  # Come è stato risolto il conflitto


class MultiDomainManager:
    """
    Gestisce la logica multi-dominio con risoluzione intelligente dei conflitti
    """
    
    def __init__(self):
        self.domains: Dict[str, DomainInfo] = {}
        self.conflicts: List[KeywordConflict] = []
        self.resolved_keywords: Dict[str, Set[str]] = {}  # keyword -> set of domains where it's valid
        self.global_non_keywords: Set[str] = set()  # Keywords che sono non-keywords in TUTTI i domini
    
    def add_domain(self, name: str, keywords: Set[str], non_keywords: Set[str], 
                   priority: int = 0, description: str = None) -> None:
        """Aggiunge un dominio al manager"""
        self.domains[name] = DomainInfo(
            name=name,
            keywords=set(keywords),
            non_keywords=set(non_keywords),
            priority=priority,
            description=description
        )
        logger.info(f"Added domain '{name}' with {len(keywords)} keywords, {len(non_keywords)} non-keywords")
        
        # Ricalcola i conflitti quando viene aggiunto un nuovo dominio
        self._analyze_conflicts()
    
    def remove_domain(self, name: str) -> bool:
        """Rimuove un dominio dal manager"""
        if name in self.domains:
            del self.domains[name]
            self._analyze_conflicts()
            logger.info(f"Removed domain '{name}'")
            return True
        return False
    
    def _analyze_conflicts(self) -> None:
        """Analizza e risolve i conflitti tra domini"""
        self.conflicts.clear()
        self.resolved_keywords.clear()
        self.global_non_keywords.clear()
        
        if not self.domains:
            return
        
        # Raccogli tutte le keywords e non-keywords
        all_keywords = set()
        all_non_keywords = set()
        
        for domain in self.domains.values():
            all_keywords.update(domain.keywords)
            all_non_keywords.update(domain.non_keywords)
        
        # Trova conflitti: termini che sono keywords in alcuni domini e non-keywords in altri
        conflicting_terms = all_keywords.intersection(all_non_keywords)
        
        for term in conflicting_terms:
            domains_with_keyword = [name for name, domain in self.domains.items() 
                                  if term in domain.keywords]
            domains_with_non_keyword = [name for name, domain in self.domains.items() 
                                      if term in domain.non_keywords]
            
            conflict = KeywordConflict(
                keyword=term,
                domains_with_keyword=domains_with_keyword,
                domains_with_non_keyword=domains_with_non_keyword
            )
            
            # Risolvi il conflitto
            resolution = self._resolve_conflict(conflict)
            conflict.resolution = resolution
            self.conflicts.append(conflict)
        
        # Identifica global non-keywords (termini che sono non-keywords in TUTTI i domini)
        if self.domains:
            self.global_non_keywords = set.intersection(
                *[domain.non_keywords for domain in self.domains.values()]
            )
        
        logger.info(f"Analyzed {len(conflicting_terms)} conflicts, "
                   f"{len(self.global_non_keywords)} global non-keywords")
    
    def _resolve_conflict(self, conflict: KeywordConflict) -> str:
        """
        Risolve un conflitto tra domini per una keyword
        
        Strategia di risoluzione:
        1. Se più domini hanno la keyword che non-keyword -> keyword vince
        2. Se parità, usa la priorità dei domini
        3. Se ancora parità, keyword vince (approccio permissivo)
        """
        keyword_count = len(conflict.domains_with_keyword)
        non_keyword_count = len(conflict.domains_with_non_keyword)
        
        # Strategia 1: Maggioranza numerica
        if keyword_count > non_keyword_count:
            self._add_resolved_keyword(conflict.keyword, conflict.domains_with_keyword)
            return f"keyword_wins_majority ({keyword_count} vs {non_keyword_count})"
        elif non_keyword_count > keyword_count:
            # Non aggiungere alla whitelist
            return f"non_keyword_wins_majority ({non_keyword_count} vs {keyword_count})"
        
        # Strategia 2: Priorità dei domini (in caso di parità numerica)
        keyword_max_priority = max(
            (self.domains[domain].priority for domain in conflict.domains_with_keyword),
            default=0
        )
        non_keyword_max_priority = max(
            (self.domains[domain].priority for domain in conflict.domains_with_non_keyword),
            default=0
        )
        
        if keyword_max_priority > non_keyword_max_priority:
            self._add_resolved_keyword(conflict.keyword, conflict.domains_with_keyword)
            return f"keyword_wins_priority ({keyword_max_priority} vs {non_keyword_max_priority})"
        elif non_keyword_max_priority > keyword_max_priority:
            return f"non_keyword_wins_priority ({non_keyword_max_priority} vs {keyword_max_priority})"
        
        # Strategia 3: Default permissivo (keyword vince)
        self._add_resolved_keyword(conflict.keyword, conflict.domains_with_keyword)
        return "keyword_wins_default_permissive"
    
    def _add_resolved_keyword(self, keyword: str, domains: List[str]) -> None:
        """Aggiunge una keyword risolta alla lista"""
        if keyword not in self.resolved_keywords:
            self.resolved_keywords[keyword] = set()
        self.resolved_keywords[keyword].update(domains)
    
    def get_effective_keywords(self, domain_name: Optional[str] = None) -> Set[str]:
        """
        Restituisce le keywords effettive per un dominio o globalmente
        
        Args:
            domain_name: Nome del dominio specifico, None per keywords globali
            
        Returns:
            Set di keywords effettive dopo risoluzione conflitti
        """
        if domain_name and domain_name in self.domains:
            # Keywords specifiche del dominio
            domain_keywords = self.domains[domain_name].keywords.copy()
            
            # Aggiungi keywords risolte che includono questo dominio
            for keyword, valid_domains in self.resolved_keywords.items():
                if domain_name in valid_domains:
                    domain_keywords.add(keyword)
            
            # Rimuovi global non-keywords
            domain_keywords.difference_update(self.global_non_keywords)
            
            return domain_keywords
        else:
            # Keywords globali (unione di tutti i domini)
            all_keywords = set()
            for domain in self.domains.values():
                all_keywords.update(domain.keywords)
            
            # Aggiungi tutte le keywords risolte
            for keyword in self.resolved_keywords:
                all_keywords.add(keyword)
            
            # Rimuovi global non-keywords
            all_keywords.difference_update(self.global_non_keywords)
            
            return all_keywords
    
    def get_effective_non_keywords(self, domain_name: Optional[str] = None) -> Set[str]:
        """Restituisce le non-keywords effettive"""
        if domain_name and domain_name in self.domains:
            return self.domains[domain_name].non_keywords.copy()
        else:
            # Global non-keywords
            return self.global_non_keywords.copy()
    
    def is_keyword_valid(self, keyword: str, domain_name: Optional[str] = None) -> bool:
        """Verifica se una keyword è valida per un dominio"""
        effective_keywords = self.get_effective_keywords(domain_name)
        return keyword.lower() in {k.lower() for k in effective_keywords}
    
    def get_conflict_report(self) -> Dict[str, Any]:
        """Genera un report dettagliato sui conflitti"""
        report = {
            'total_conflicts': len(self.conflicts),
            'total_domains': len(self.domains),
            'global_non_keywords_count': len(self.global_non_keywords),
            'conflicts_by_resolution': defaultdict(int),
            'detailed_conflicts': []
        }
        
        for conflict in self.conflicts:
            report['conflicts_by_resolution'][conflict.resolution] += 1
            report['detailed_conflicts'].append({
                'keyword': conflict.keyword,
                'domains_with_keyword': conflict.domains_with_keyword,
                'domains_with_non_keyword': conflict.domains_with_non_keyword,
                'resolution': conflict.resolution
            })
        
        return report
    
    def get_domain_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche sui domini"""
        stats = {}
        
        for name, domain in self.domains.items():
            effective_keywords = self.get_effective_keywords(name)
            stats[name] = {
                'original_keywords': len(domain.keywords),
                'original_non_keywords': len(domain.non_keywords),
                'effective_keywords': len(effective_keywords),
                'priority': domain.priority,
                'description': domain.description
            }
        
        return stats
    
    def optimize_domains(self) -> Dict[str, Any]:
        """
        Ottimizza i domini rimuovendo ridondanze e migliorando le performance
        
        Returns:
            Report delle ottimizzazioni applicate
        """
        optimizations = {
            'removed_redundant_keywords': 0,
            'removed_redundant_non_keywords': 0,
            'details': []
        }
        
        # Rimuovi keywords che sono anche global non-keywords (ridondanti)
        for domain_name, domain in self.domains.items():
            redundant_keywords = domain.keywords.intersection(self.global_non_keywords)
            if redundant_keywords:
                domain.keywords.difference_update(redundant_keywords)
                optimizations['removed_redundant_keywords'] += len(redundant_keywords)
                optimizations['details'].append(
                    f"Removed {len(redundant_keywords)} redundant keywords from {domain_name}"
                )
        
        # Ricalcola conflitti dopo ottimizzazione
        self._analyze_conflicts()
        
        logger.info(f"Domain optimization completed: {optimizations}")
        return optimizations
