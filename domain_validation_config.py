#!/usr/bin/env python3
"""
Domain Validation Configuration - Sistema dinamico per configurazione validazione multi-dominio
Sostituisce la logica hardcoded automotive con configurazione dinamica per dominio
"""

import re
import logging
from typing import Dict, List, Set, Optional, Any, Pattern
from dataclasses import dataclass, field
from domain_config import get_domain_config, DomainConfig

logger = logging.getLogger(__name__)


@dataclass
class ValidationPatterns:
    """Pattern di validazione per un dominio specifico"""
    compound_terms: List[Pattern] = field(default_factory=list)
    contextual_terms: List[Pattern] = field(default_factory=list)
    technical_terms: List[Pattern] = field(default_factory=list)
    domain_specific_patterns: List[Pattern] = field(default_factory=list)


@dataclass
class DomainValidationConfig:
    """Configurazione di validazione per un dominio specifico"""
    domain_name: str
    keywords: Set[str] = field(default_factory=set)
    non_keywords: Set[str] = field(default_factory=set)
    patterns: ValidationPatterns = field(default_factory=ValidationPatterns)
    rejection_message: str = ""
    technical_terms: Set[str] = field(default_factory=set)
    
    def __post_init__(self):
        if not self.rejection_message:
            self.rejection_message = f"Query does not appear to be related to {self.domain_name.lower()} topics"


class DomainValidationManager:
    """
    Gestisce la configurazione di validazione dinamica per tutti i domini
    Sostituisce la logica hardcoded automotive
    """
    
    def __init__(self):
        self.domain_configs: Dict[str, DomainValidationConfig] = {}
        self._load_all_domain_configs()
    
    def _load_all_domain_configs(self) -> None:
        """Carica le configurazioni di validazione per tutti i domini disponibili"""
        from domain_config import DOMAIN_CONFIGS
        
        for domain_name in DOMAIN_CONFIGS.keys():
            try:
                self.domain_configs[domain_name] = self._create_domain_validation_config(domain_name)
                logger.info(f"Loaded validation config for domain: {domain_name}")
            except Exception as e:
                logger.error(f"Failed to load validation config for domain {domain_name}: {e}")
    
    def _create_domain_validation_config(self, domain_name: str) -> DomainValidationConfig:
        """Crea la configurazione di validazione per un dominio specifico"""
        domain_config = get_domain_config(domain_name)
        
        # Estrai keywords e non_keywords dalla configurazione del dominio
        keywords = set(domain_config.keywords) if hasattr(domain_config, 'keywords') else set()
        non_keywords = set(domain_config.non_keywords) if hasattr(domain_config, 'non_keywords') else set()
        
        # Crea pattern specifici per il dominio
        patterns = self._create_domain_patterns(domain_name, keywords)
        
        # Estrai termini tecnici dalla configurazione
        technical_terms = self._extract_technical_terms(domain_name, keywords)
        
        return DomainValidationConfig(
            domain_name=domain_name,
            keywords=keywords,
            non_keywords=non_keywords,
            patterns=patterns,
            rejection_message=domain_config.rejection_message,
            technical_terms=technical_terms
        )
    
    def _create_domain_patterns(self, domain_name: str, keywords: Set[str]) -> ValidationPatterns:
        """Crea pattern di validazione specifici per il dominio"""
        patterns = ValidationPatterns()
        
        if domain_name == 'AUTOMOTIVE':
            # Pattern specifici automotive (migrati dalla logica hardcoded)
            patterns.compound_terms = [
                re.compile(r'\b(?:corpo\s+farfallato|albero\s+motore|testa\s+cilindro)\b', re.IGNORECASE),
                re.compile(r'\b(?:filtro\s+aria|olio\s+motore|liquido\s+freni)\b', re.IGNORECASE)
            ]
            patterns.contextual_terms = [
                re.compile(r'\b([a-z]+)\s+(?:per|di|del|della|dei|delle)\s+(?:motore|moto|scooter|veicolo)\b', re.IGNORECASE),
                re.compile(r'\b(?:sostituzione|riparazione|manutenzione)\s+([a-z]+)\b', re.IGNORECASE)
            ]
            patterns.technical_terms = [
                re.compile(r'\b(motore|cilindro|valvola|pistone)\b', re.IGNORECASE)
            ]
        
        elif domain_name == 'FINANCE':
            patterns.compound_terms = [
                re.compile(r'\b(?:tasso\s+interesse|conto\s+corrente|carta\s+credito)\b', re.IGNORECASE),
                re.compile(r'\b(?:mutuo\s+casa|prestito\s+personale|investimento\s+fondi)\b', re.IGNORECASE)
            ]
            patterns.contextual_terms = [
                re.compile(r'\b([a-z]+)\s+(?:bancario|finanziario|creditizio)\b', re.IGNORECASE),
                re.compile(r'\b(?:calcolo|gestione|analisi)\s+([a-z]+)\b', re.IGNORECASE)
            ]
            patterns.technical_terms = [
                re.compile(r'\b(interesse|capitale|dividendo|rendimento)\b', re.IGNORECASE)
            ]
        
        elif domain_name == 'HEALTHCARE':
            patterns.compound_terms = [
                re.compile(r'\b(?:pressione\s+sanguigna|battito\s+cardiaco|esame\s+sangue)\b', re.IGNORECASE),
                re.compile(r'\b(?:terapia\s+farmacologica|diagnosi\s+medica)\b', re.IGNORECASE)
            ]
            patterns.contextual_terms = [
                re.compile(r'\b([a-z]+)\s+(?:medico|clinico|sanitario)\b', re.IGNORECASE),
                re.compile(r'\b(?:trattamento|cura|terapia)\s+([a-z]+)\b', re.IGNORECASE)
            ]
            patterns.technical_terms = [
                re.compile(r'\b(diagnosi|sintomo|farmaco|terapia)\b', re.IGNORECASE)
            ]
        
        # Aggiungi pattern generici basati sulle keywords del dominio
        if keywords:
            keyword_pattern = '|'.join(re.escape(kw) for kw in list(keywords)[:20])  # Limita per performance
            patterns.domain_specific_patterns = [
                re.compile(f'\\b({keyword_pattern})\\b', re.IGNORECASE)
            ]
        
        return patterns
    
    def _extract_technical_terms(self, domain_name: str, keywords: Set[str]) -> Set[str]:
        """Estrae termini tecnici specifici per il dominio"""
        # Filtra keywords per lunghezza e complessità per identificare termini tecnici
        technical_terms = set()
        
        for keyword in keywords:
            # Considera tecnici i termini con più di 4 caratteri e senza spazi
            if len(keyword) > 4 and ' ' not in keyword and keyword.isalpha():
                technical_terms.add(keyword.lower())
        
        # Aggiungi termini tecnici specifici per dominio
        domain_specific_terms = {
            'AUTOMOTIVE': {'motore', 'cilindro', 'valvola', 'pistone', 'frizione', 'cambio'},
            'FINANCE': {'interesse', 'capitale', 'dividendo', 'rendimento', 'liquidità', 'volatilità'},
            'HEALTHCARE': {'diagnosi', 'sintomo', 'farmaco', 'terapia', 'patologia', 'clinico'},
            'HR': {'dipendente', 'contratto', 'stipendio', 'formazione', 'valutazione', 'recruiting'},
            'LEGAL': {'contratto', 'clausola', 'normativa', 'giurisprudenza', 'procedura', 'tribunale'}
        }
        
        if domain_name in domain_specific_terms:
            technical_terms.update(domain_specific_terms[domain_name])
        
        return technical_terms
    
    def get_domain_validation_config(self, domain_name: str) -> DomainValidationConfig:
        """Restituisce la configurazione di validazione per un dominio"""
        if domain_name not in self.domain_configs:
            raise ValueError(f"No validation config found for domain: {domain_name}")
        return self.domain_configs[domain_name]
    
    def get_keywords_for_domain(self, domain_name: str) -> Set[str]:
        """Restituisce le keywords per un dominio specifico"""
        config = self.get_domain_validation_config(domain_name)
        return config.keywords.copy()
    
    def get_technical_terms_for_domain(self, domain_name: str) -> Set[str]:
        """Restituisce i termini tecnici per un dominio specifico"""
        config = self.get_domain_validation_config(domain_name)
        return config.technical_terms.copy()
    
    def get_patterns_for_domain(self, domain_name: str) -> ValidationPatterns:
        """Restituisce i pattern di validazione per un dominio"""
        config = self.get_domain_validation_config(domain_name)
        return config.patterns
    
    def get_rejection_message_for_domain(self, domain_name: str) -> str:
        """Restituisce il messaggio di rifiuto per un dominio"""
        config = self.get_domain_validation_config(domain_name)
        return config.rejection_message
    
    def validate_term_for_domain(self, term: str, domain_name: str) -> bool:
        """Valida se un termine è rilevante per un dominio specifico"""
        config = self.get_domain_validation_config(domain_name)
        term_lower = term.lower()
        
        # Controlla keywords dirette
        if term_lower in config.keywords:
            return True
        
        # Controlla termini tecnici
        if term_lower in config.technical_terms:
            return True
        
        # Controlla pattern
        for pattern_list in [config.patterns.compound_terms, 
                           config.patterns.contextual_terms,
                           config.patterns.technical_terms,
                           config.patterns.domain_specific_patterns]:
            for pattern in pattern_list:
                if pattern.search(term):
                    return True
        
        return False
    
    def get_available_domains(self) -> List[str]:
        """Restituisce la lista dei domini disponibili"""
        return list(self.domain_configs.keys())
    
    def reload_domain_config(self, domain_name: str) -> None:
        """Ricarica la configurazione per un dominio specifico"""
        try:
            self.domain_configs[domain_name] = self._create_domain_validation_config(domain_name)
            logger.info(f"Reloaded validation config for domain: {domain_name}")
        except Exception as e:
            logger.error(f"Failed to reload validation config for domain {domain_name}: {e}")


# Istanza globale del manager
domain_validation_manager: Optional[DomainValidationManager] = None


def get_domain_validation_manager() -> DomainValidationManager:
    """Restituisce l'istanza globale del domain validation manager"""
    global domain_validation_manager
    if domain_validation_manager is None:
        domain_validation_manager = DomainValidationManager()
    return domain_validation_manager


def initialize_domain_validation_manager() -> DomainValidationManager:
    """Inizializza il domain validation manager"""
    global domain_validation_manager
    domain_validation_manager = DomainValidationManager()
    return domain_validation_manager
