#!/usr/bin/env python3
"""
Blacklist Manager - Gestione configurabile dei termini inappropriati
Separa la logica di filtraggio dalla configurazione hardcoded
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Eccezione per errori di validazione del sistema"""
    pass


class ConfigurationError(Exception):
    """Eccezione per errori di configurazione"""
    pass


@dataclass
class BlacklistCategory:
    """Rappresenta una categoria di termini inappropriati"""
    name: str
    enabled: bool
    terms: Set[str]
    description: Optional[str] = None


class BlacklistManager:
    """
    Gestisce la configurazione e il caricamento dei termini inappropriati
    da file di configurazione esterni invece di hardcoding nel codice
    """
    
    def __init__(self, config_file: str = "blacklist_config.json"):
        self.config_file = Path(config_file)
        self.categories: Dict[str, BlacklistCategory] = {}
        self.settings: Dict[str, Any] = {}
        self.custom_terms: Set[str] = set()
        self._load_configuration()
    
    def _load_configuration(self) -> None:
        """Carica la configurazione dal file JSON"""
        try:
            if not self.config_file.exists():
                logger.warning(f"Blacklist config file not found: {self.config_file}")
                self._create_default_config()
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self._parse_configuration(config)
            logger.info(f"Blacklist configuration loaded from {self.config_file}")
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in blacklist config: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading blacklist config: {e}")
    
    def _parse_configuration(self, config: Dict[str, Any]) -> None:
        """Parsa la configurazione caricata"""
        # Carica le categorie
        categories_config = config.get('categories', {})
        for category_name, category_data in categories_config.items():
            self.categories[category_name] = BlacklistCategory(
                name=category_name,
                enabled=category_data.get('enabled', True),
                terms=set(term.lower() for term in category_data.get('terms', [])),
                description=category_data.get('description')
            )
        
        # Carica le impostazioni
        self.settings = config.get('settings', {})
        
        # Carica termini personalizzati
        custom_terms = self.settings.get('custom_terms', [])
        self.custom_terms = set(term.lower() for term in custom_terms)
        
        logger.info(f"Loaded {len(self.categories)} blacklist categories")
        logger.info(f"Total terms: {len(self.get_all_terms())}")
    
    def _create_default_config(self) -> None:
        """Crea una configurazione di default se il file non esiste"""
        default_config = {
            "version": "1.0",
            "description": "Default blacklist configuration",
            "categories": {
                "basic_profanity": {
                    "enabled": True,
                    "terms": ["spam", "test_inappropriate"]
                }
            },
            "settings": {
                "case_sensitive": False,
                "partial_match": False,
                "custom_terms": []
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            self._parse_configuration(default_config)
            logger.info(f"Created default blacklist config at {self.config_file}")
            
        except Exception as e:
            logger.error(f"Failed to create default config: {e}")
            # Fallback to minimal in-memory config
            self.categories = {}
            self.settings = {"case_sensitive": False, "partial_match": False}
            self.custom_terms = set()
    
    def get_all_terms(self) -> Set[str]:
        """Restituisce tutti i termini attivi dalla blacklist"""
        all_terms = set()
        
        # Aggiungi termini dalle categorie abilitate
        for category in self.categories.values():
            if category.enabled:
                all_terms.update(category.terms)
        
        # Aggiungi termini personalizzati
        all_terms.update(self.custom_terms)
        
        return all_terms
    
    def get_category_terms(self, category_name: str) -> Set[str]:
        """Restituisce i termini di una categoria specifica"""
        category = self.categories.get(category_name)
        if category and category.enabled:
            return category.terms.copy()
        return set()
    
    def is_category_enabled(self, category_name: str) -> bool:
        """Verifica se una categoria è abilitata"""
        category = self.categories.get(category_name)
        return category.enabled if category else False
    
    def enable_category(self, category_name: str) -> bool:
        """Abilita una categoria"""
        if category_name in self.categories:
            self.categories[category_name].enabled = True
            logger.info(f"Enabled blacklist category: {category_name}")
            return True
        return False
    
    def disable_category(self, category_name: str) -> bool:
        """Disabilita una categoria"""
        if category_name in self.categories:
            self.categories[category_name].enabled = False
            logger.info(f"Disabled blacklist category: {category_name}")
            return True
        return False
    
    def add_custom_terms(self, terms: List[str]) -> None:
        """Aggiunge termini personalizzati"""
        case_sensitive = self.settings.get('case_sensitive', False)
        new_terms = set(terms if case_sensitive else [t.lower() for t in terms])
        self.custom_terms.update(new_terms)
        logger.info(f"Added {len(new_terms)} custom terms to blacklist")
    
    def remove_custom_terms(self, terms: List[str]) -> None:
        """Rimuove termini personalizzati"""
        case_sensitive = self.settings.get('case_sensitive', False)
        terms_to_remove = set(terms if case_sensitive else [t.lower() for t in terms])
        self.custom_terms.difference_update(terms_to_remove)
        logger.info(f"Removed {len(terms_to_remove)} custom terms from blacklist")
    
    def save_configuration(self) -> None:
        """Salva la configurazione corrente nel file"""
        try:
            # Ricarica la configurazione esistente per preservare metadati
            config = {}
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # Aggiorna le categorie
            config['categories'] = {}
            for name, category in self.categories.items():
                config['categories'][name] = {
                    'enabled': category.enabled,
                    'terms': sorted(list(category.terms))
                }
                if category.description:
                    config['categories'][name]['description'] = category.description
            
            # Aggiorna le impostazioni
            if 'settings' not in config:
                config['settings'] = {}
            config['settings'].update(self.settings)
            config['settings']['custom_terms'] = sorted(list(self.custom_terms))
            
            # Salva nel file
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Blacklist configuration saved to {self.config_file}")
            
        except Exception as e:
            raise ConfigurationError(f"Failed to save blacklist config: {e}")
    
    def reload_configuration(self) -> None:
        """Ricarica la configurazione dal file"""
        self.categories.clear()
        self.custom_terms.clear()
        self._load_configuration()
        logger.info("Blacklist configuration reloaded")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche sulla configurazione"""
        enabled_categories = [name for name, cat in self.categories.items() if cat.enabled]
        disabled_categories = [name for name, cat in self.categories.items() if not cat.enabled]
        
        return {
            'total_categories': len(self.categories),
            'enabled_categories': len(enabled_categories),
            'disabled_categories': len(disabled_categories),
            'enabled_category_names': enabled_categories,
            'disabled_category_names': disabled_categories,
            'total_terms': len(self.get_all_terms()),
            'custom_terms_count': len(self.custom_terms),
            'settings': self.settings.copy()
        }
    
    def validate_term(self, term: str) -> bool:
        """Verifica se un termine è nella blacklist"""
        if not term:
            return False
        
        case_sensitive = self.settings.get('case_sensitive', False)
        search_term = term if case_sensitive else term.lower()
        all_terms = self.get_all_terms()
        
        # Match esatto
        if search_term in all_terms:
            return True
        
        # Match parziale se abilitato
        if self.settings.get('partial_match', False):
            for blacklist_term in all_terms:
                if blacklist_term in search_term or search_term in blacklist_term:
                    return True
        
        return False


# Istanza globale del manager
blacklist_manager: Optional[BlacklistManager] = None


def get_blacklist_manager() -> BlacklistManager:
    """Restituisce l'istanza globale del blacklist manager"""
    global blacklist_manager
    if blacklist_manager is None:
        blacklist_manager = BlacklistManager()
    return blacklist_manager


def initialize_blacklist_manager(config_file: str = "blacklist_config.json") -> BlacklistManager:
    """Inizializza il blacklist manager con un file di configurazione specifico"""
    global blacklist_manager
    blacklist_manager = BlacklistManager(config_file)
    return blacklist_manager
