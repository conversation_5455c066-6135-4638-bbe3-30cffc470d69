# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json

# Pyre type checker
.pyre/

# dotenv
.env
.env.*

# Logs
logs/
*.log

# VS Code
.vscode/

# MacOS
.DS_Store

# PDF/Document cache
cache/

# Project-specific
sorgenti/*/link/*.pdf
sorgenti/*/nolink/*.pdf

# Cache files that cause reload issues
document_whitelist_cache.json
*.cache
*.db
*.sqlite
cache_metadata.json
faiss_index/
embeddings_cache/
