#!/usr/bin/env python3
"""
Multi-Installation Management Utility for Softway Chatbot SYM

This script helps manage multiple installations on the same server.

Usage:
    python manage_installations.py list
    python manage_installations.py status
    python manage_installations.py create <installation_id>
    python manage_installations.py clone <source_id> <target_id>
    python manage_installations.py remove <installation_id>
    python manage_installations.py start <installation_id>
    python manage_installations.py stop <installation_id>
"""

import argparse
import asyncio
import json
import logging
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional

# Optional import for system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

class InstallationManager:
    """Manages multiple installations of the chatbot system"""
    
    def __init__(self):
        self.base_dir = Path.cwd()
        self.installations_file = self.base_dir / "installations.json"
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging for the manager"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def load_installations(self) -> Dict:
        """Load installations registry"""
        if not self.installations_file.exists():
            return {}
        
        try:
            with open(self.installations_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load installations registry: {e}")
            return {}
    
    def save_installations(self, installations: Dict):
        """Save installations registry"""
        try:
            with open(self.installations_file, 'w') as f:
                json.dump(installations, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save installations registry: {e}")
    
    def list_installations(self):
        """List all registered installations"""
        installations = self.load_installations()
        
        if not installations:
            print("No installations registered.")
            return
        
        print("📋 Registered Installations:")
        print("=" * 60)
        
        for install_id, info in installations.items():
            status = self.get_installation_status(install_id)
            print(f"🔹 {install_id}")
            print(f"   Path: {info.get('path', 'Unknown')}")
            print(f"   Port: {info.get('port', 'Unknown')}")
            print(f"   Database: {info.get('database', 'Unknown')}")
            print(f"   Status: {status}")
            print()
    
    def get_installation_status(self, installation_id: str) -> str:
        """Get the status of an installation"""
        installations = self.load_installations()

        if installation_id not in installations:
            return "❌ Not registered"

        info = installations[installation_id]
        port = info.get('port')

        if not port:
            return "⚠️ No port configured"

        if not PSUTIL_AVAILABLE:
            return "⚠️ Status unknown (psutil not available)"

        # Check if port is in use
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    return "🟢 Running"
        except:
            pass

        return "🔴 Stopped"
    
    def status_all(self):
        """Show status of all installations"""
        installations = self.load_installations()
        
        if not installations:
            print("No installations registered.")
            return
        
        print("📊 Installation Status:")
        print("=" * 60)
        
        for install_id in installations:
            status = self.get_installation_status(install_id)
            print(f"{install_id}: {status}")
    
    def create_installation(self, installation_id: str, port: int = None):
        """Create a new installation"""
        installations = self.load_installations()
        
        if installation_id in installations:
            print(f"❌ Installation '{installation_id}' already exists")
            return False
        
        # Auto-assign port if not provided
        if port is None:
            port = self._find_available_port()
        
        install_path = self.base_dir / f"installation_{installation_id}"
        
        print(f"🚀 Creating installation '{installation_id}'...")
        print(f"   Path: {install_path}")
        print(f"   Port: {port}")
        
        try:
            # Create installation directory
            install_path.mkdir(exist_ok=True)
            
            # Copy current installation
            self._copy_installation(self.base_dir, install_path)
            
            # Create custom .env
            self._create_env_file(install_path, installation_id, port)
            
            # Register installation
            installations[installation_id] = {
                'path': str(install_path),
                'port': port,
                'database': f"softway_chat_{installation_id}",
                'created': str(Path().cwd())
            }
            
            self.save_installations(installations)
            
            print(f"✅ Installation '{installation_id}' created successfully")
            print(f"📝 Next steps:")
            print(f"   1. cd {install_path}")
            print(f"   2. Edit .env file if needed")
            print(f"   3. python setup_database.py")
            print(f"   4. python start_web.py")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create installation: {e}")
            return False
    
    def _find_available_port(self, start_port: int = 8000) -> int:
        """Find an available port starting from start_port"""
        installations = self.load_installations()
        used_ports = {info.get('port') for info in installations.values()}
        
        port = start_port
        while port in used_ports or self._is_port_in_use(port):
            port += 1
        
        return port
    
    def _is_port_in_use(self, port: int) -> bool:
        """Check if a port is currently in use"""
        if not PSUTIL_AVAILABLE:
            return False  # Assume port is available if we can't check

        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port:
                    return True
        except:
            pass
        return False
    
    def _copy_installation(self, source: Path, target: Path):
        """Copy installation files excluding data directories"""
        exclude_dirs = {
            'cache', 'logs', 'temp', 'uploads', '__pycache__', 
            '.git', 'env', 'venv', '.venv'
        }
        exclude_files = {
            '.env', 'installations.json'
        }
        
        for item in source.iterdir():
            if item.name.startswith('.') and item.name not in {'.env.example'}:
                continue
            
            if item.name in exclude_dirs or item.name in exclude_files:
                continue
            
            target_path = target / item.name
            
            if item.is_file():
                shutil.copy2(item, target_path)
            elif item.is_dir():
                shutil.copytree(item, target_path, ignore=shutil.ignore_patterns('__pycache__'))
    
    def _create_env_file(self, install_path: Path, installation_id: str, port: int):
        """Create .env file for the new installation"""
        env_example = self.base_dir / '.env.example'
        env_file = install_path / '.env'
        
        if not env_example.exists():
            raise FileNotFoundError(".env.example not found")
        
        # Read template
        with open(env_example, 'r') as f:
            content = f.read()
        
        # Replace placeholders
        content = content.replace('INSTALLATION_ID=default', f'INSTALLATION_ID={installation_id}')
        content = content.replace('WEB_PORT=8000', f'WEB_PORT={port}')
        content = content.replace('DB_DATABASE=softway_chat', f'DB_DATABASE=softway_chat_{installation_id}')
        
        # Write new .env
        with open(env_file, 'w') as f:
            f.write(content)
    
    def remove_installation(self, installation_id: str, force: bool = False):
        """Remove an installation"""
        installations = self.load_installations()
        
        if installation_id not in installations:
            print(f"❌ Installation '{installation_id}' not found")
            return False
        
        info = installations[installation_id]
        install_path = Path(info['path'])
        
        if not force:
            response = input(f"⚠️ This will permanently delete installation '{installation_id}' at {install_path}. Continue? (y/N): ")
            if response.lower() != 'y':
                print("Cancelled.")
                return False
        
        try:
            # Remove directory
            if install_path.exists():
                shutil.rmtree(install_path)
            
            # Remove from registry
            del installations[installation_id]
            self.save_installations(installations)
            
            print(f"✅ Installation '{installation_id}' removed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to remove installation: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(
        description="Manage multiple chatbot installations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    subparsers.add_parser('list', help='List all installations')
    
    # Status command
    subparsers.add_parser('status', help='Show status of all installations')
    
    # Create command
    create_parser = subparsers.add_parser('create', help='Create new installation')
    create_parser.add_argument('installation_id', help='ID for the new installation')
    create_parser.add_argument('--port', type=int, help='Port for web server')
    
    # Remove command
    remove_parser = subparsers.add_parser('remove', help='Remove installation')
    remove_parser.add_argument('installation_id', help='ID of installation to remove')
    remove_parser.add_argument('--force', action='store_true', help='Skip confirmation')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = InstallationManager()
    
    if args.command == 'list':
        manager.list_installations()
    elif args.command == 'status':
        manager.status_all()
    elif args.command == 'create':
        manager.create_installation(args.installation_id, args.port)
    elif args.command == 'remove':
        manager.remove_installation(args.installation_id, args.force)

if __name__ == "__main__":
    main()
