"""
Advanced Cache Manager for Document Processing
Provides multi-level caching with flexible storage backends and intelligent invalidation
"""

import asyncio
import hashlib
import json
import logging
import pickle
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np

from config import config

# Optional Redis support
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Represents a cache entry with metadata"""
    key: str
    data: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    file_hash: Optional[str] = None
    file_size: Optional[int] = None
    expires_at: Optional[datetime] = None
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        if self.expires_at:
            return datetime.now() > self.expires_at
        return False
    
    def touch(self):
        """Update access information"""
        self.last_accessed = datetime.now()
        self.access_count += 1

@dataclass
class CacheStats:
    """Cache statistics"""
    total_entries: int = 0
    total_size_mb: float = 0.0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0

class CacheBackend(ABC):
    """Abstract base class for cache storage backends"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key"""
        pass
    
    @abstractmethod
    async def set(self, key: str, entry: CacheEntry) -> bool:
        """Set cache entry"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete cache entry"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    async def keys(self) -> List[str]:
        """Get all cache keys"""
        pass
    
    @abstractmethod
    async def size(self) -> int:
        """Get cache size in bytes"""
        pass

class FilesystemCacheBackend(CacheBackend):
    """Filesystem-based cache backend"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._metadata: Dict[str, Dict] = {}
        self._load_metadata()
    
    def _load_metadata(self):
        """Load cache metadata from disk"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    self._metadata = json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache metadata: {e}")
            self._metadata = {}
    
    def _save_metadata(self):
        """Save cache metadata to disk"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self._metadata, f, default=str, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache metadata: {e}")
    
    def _get_cache_file_path(self, key: str) -> Path:
        """Get cache file path for key"""
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            if not cache_file.exists():
                return None
            
            # Load cache entry
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            # Load metadata
            metadata = self._metadata.get(key, {})
            
            entry = CacheEntry(
                key=key,
                data=data,
                created_at=datetime.fromisoformat(metadata.get('created_at', datetime.now().isoformat())),
                last_accessed=datetime.fromisoformat(metadata.get('last_accessed', datetime.now().isoformat())),
                access_count=metadata.get('access_count', 0),
                file_hash=metadata.get('file_hash'),
                file_size=metadata.get('file_size'),
                expires_at=datetime.fromisoformat(metadata['expires_at']) if metadata.get('expires_at') else None
            )
            
            # Check if expired
            if entry.is_expired():
                await self.delete(key)
                return None
            
            # Update access info
            entry.touch()
            self._metadata[key] = {
                'created_at': entry.created_at.isoformat(),
                'last_accessed': entry.last_accessed.isoformat(),
                'access_count': entry.access_count,
                'file_hash': entry.file_hash,
                'file_size': entry.file_size,
                'expires_at': entry.expires_at.isoformat() if entry.expires_at else None
            }
            self._save_metadata()
            
            return entry
            
        except Exception as e:
            logger.error(f"Error getting cache entry {key}: {e}")
            return None
    
    async def set(self, key: str, entry: CacheEntry) -> bool:
        """Set cache entry"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            # Save data
            with open(cache_file, 'wb') as f:
                pickle.dump(entry.data, f)
            
            # Save metadata
            self._metadata[key] = {
                'created_at': entry.created_at.isoformat(),
                'last_accessed': entry.last_accessed.isoformat(),
                'access_count': entry.access_count,
                'file_hash': entry.file_hash,
                'file_size': entry.file_size,
                'expires_at': entry.expires_at.isoformat() if entry.expires_at else None
            }
            self._save_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache entry {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete cache entry"""
        try:
            cache_file = self._get_cache_file_path(key)
            
            if cache_file.exists():
                cache_file.unlink()
            
            if key in self._metadata:
                del self._metadata[key]
                self._save_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting cache entry {key}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries"""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            
            self._metadata = {}
            self._save_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    async def keys(self) -> List[str]:
        """Get all cache keys"""
        return list(self._metadata.keys())
    
    async def size(self) -> int:
        """Get cache size in bytes"""
        try:
            total_size = 0
            for cache_file in self.cache_dir.glob("*.cache"):
                total_size += cache_file.stat().st_size
            return total_size
        except Exception:
            return 0

class RedisCacheBackend(CacheBackend):
    """Redis-based cache backend for production environments"""

    def __init__(self, redis_url: str = "redis://localhost:6379", db: int = 0, key_prefix: str = "docache:"):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis not available. Install with: pip install redis")

        self.redis_url = redis_url
        self.db = db
        self.key_prefix = key_prefix
        self.redis_client = None

    async def _get_client(self):
        """Get Redis client, creating if necessary"""
        if self.redis_client is None:
            self.redis_client = redis.from_url(self.redis_url, db=self.db, decode_responses=False)
        return self.redis_client

    def _make_redis_key(self, key: str) -> str:
        """Create Redis key with prefix"""
        return f"{self.key_prefix}{key}"

    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cache entry by key"""
        try:
            client = await self._get_client()
            redis_key = self._make_redis_key(key)

            # Get both data and metadata
            pipe = client.pipeline()
            pipe.get(f"{redis_key}:data")
            pipe.hgetall(f"{redis_key}:meta")
            results = await pipe.execute()

            data_bytes, metadata = results

            if not data_bytes or not metadata:
                return None

            # Deserialize data
            data = pickle.loads(data_bytes)

            # Create cache entry
            entry = CacheEntry(
                key=key,
                data=data,
                created_at=datetime.fromisoformat(metadata.get('created_at', datetime.now().isoformat())),
                last_accessed=datetime.fromisoformat(metadata.get('last_accessed', datetime.now().isoformat())),
                access_count=int(metadata.get('access_count', 0)),
                file_hash=metadata.get('file_hash'),
                file_size=int(metadata.get('file_size', 0)) if metadata.get('file_size') else None,
                expires_at=datetime.fromisoformat(metadata['expires_at']) if metadata.get('expires_at') else None
            )

            # Check if expired
            if entry.is_expired():
                await self.delete(key)
                return None

            # Update access info
            entry.touch()
            await client.hset(f"{redis_key}:meta", mapping={
                'last_accessed': entry.last_accessed.isoformat(),
                'access_count': str(entry.access_count)
            })

            return entry

        except Exception as e:
            logger.error(f"Error getting Redis cache entry {key}: {e}")
            return None

    async def set(self, key: str, entry: CacheEntry) -> bool:
        """Set cache entry"""
        try:
            client = await self._get_client()
            redis_key = self._make_redis_key(key)

            # Serialize data
            data_bytes = pickle.dumps(entry.data)

            # Prepare metadata
            metadata = {
                'created_at': entry.created_at.isoformat(),
                'last_accessed': entry.last_accessed.isoformat(),
                'access_count': str(entry.access_count),
                'file_hash': entry.file_hash or '',
                'file_size': str(entry.file_size) if entry.file_size else '',
                'expires_at': entry.expires_at.isoformat() if entry.expires_at else ''
            }

            # Set data and metadata
            pipe = client.pipeline()
            pipe.set(f"{redis_key}:data", data_bytes)
            pipe.hset(f"{redis_key}:meta", mapping=metadata)

            # Set expiration if specified
            if entry.expires_at:
                ttl_seconds = int((entry.expires_at - datetime.now()).total_seconds())
                if ttl_seconds > 0:
                    pipe.expire(f"{redis_key}:data", ttl_seconds)
                    pipe.expire(f"{redis_key}:meta", ttl_seconds)

            await pipe.execute()
            return True

        except Exception as e:
            logger.error(f"Error setting Redis cache entry {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete cache entry"""
        try:
            client = await self._get_client()
            redis_key = self._make_redis_key(key)

            pipe = client.pipeline()
            pipe.delete(f"{redis_key}:data")
            pipe.delete(f"{redis_key}:meta")
            await pipe.execute()

            return True

        except Exception as e:
            logger.error(f"Error deleting Redis cache entry {key}: {e}")
            return False

    async def clear(self) -> bool:
        """Clear all cache entries"""
        try:
            client = await self._get_client()

            # Find all keys with our prefix
            pattern = f"{self.key_prefix}*"
            keys = []
            async for key in client.scan_iter(match=pattern):
                keys.append(key)

            if keys:
                await client.delete(*keys)

            return True

        except Exception as e:
            logger.error(f"Error clearing Redis cache: {e}")
            return False

    async def keys(self) -> List[str]:
        """Get all cache keys"""
        try:
            client = await self._get_client()

            # Find all data keys and extract original keys
            pattern = f"{self.key_prefix}*:data"
            keys = []
            async for redis_key in client.scan_iter(match=pattern):
                # Remove prefix and :data suffix
                original_key = redis_key.decode().replace(self.key_prefix, '').replace(':data', '')
                keys.append(original_key)

            return keys

        except Exception as e:
            logger.error(f"Error getting Redis cache keys: {e}")
            return []

    async def size(self) -> int:
        """Get cache size in bytes (approximate)"""
        try:
            client = await self._get_client()

            total_size = 0
            pattern = f"{self.key_prefix}*:data"
            async for key in client.scan_iter(match=pattern):
                size = await client.memory_usage(key)
                if size:
                    total_size += size

            return total_size

        except Exception as e:
            logger.error(f"Error getting Redis cache size: {e}")
            return 0

    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()

class DocumentCacheManager:
    """Advanced cache manager for document processing"""
    
    def __init__(self, backend: CacheBackend, max_size_mb: int = 500, ttl_hours: int = 24):
        self.backend = backend
        self.max_size_mb = max_size_mb
        self.ttl_hours = ttl_hours
        self.stats = CacheStats()
        
        # Cache key prefixes for different data types
        self.PREFIXES = {
            'content': 'content:',
            'chunks': 'chunks:',
            'embeddings': 'embeddings:',
            'processed_doc': 'processed_doc:',
            'faiss_index': 'faiss_index:'
        }
    
    def _generate_file_hash(self, file_path: Path) -> Tuple[str, int]:
        """Generate hash and size for file-based cache invalidation"""
        try:
            stat = file_path.stat()
            # Use file path, modification time, and size for comprehensive hash
            hash_input = f"{file_path.absolute()}_{stat.st_mtime}_{stat.st_size}"
            file_hash = hashlib.sha256(hash_input.encode()).hexdigest()
            return file_hash, stat.st_size
        except Exception as e:
            logger.warning(f"Failed to generate file hash for {file_path}: {e}")
            return str(file_path), 0
    
    def _make_cache_key(self, prefix: str, identifier: str) -> str:
        """Create cache key with prefix"""
        return f"{prefix}{identifier}"
    
    async def get_content_cache(self, file_path: Path) -> Optional[Tuple[str, List[Dict], List[Dict], int]]:
        """Get cached PDF content extraction"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['content'], file_hash)
        
        entry = await self.backend.get(cache_key)
        if entry and entry.file_hash == file_hash:
            self.stats.hit_count += 1
            logger.info(f"Cache HIT for content: {file_path.name}")
            return entry.data
        
        self.stats.miss_count += 1
        logger.info(f"Cache MISS for content: {file_path.name}")
        return None
    
    async def set_content_cache(self, file_path: Path, content_data: Tuple[str, List[Dict], List[Dict], int]):
        """Cache PDF content extraction"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['content'], file_hash)
        
        entry = CacheEntry(
            key=cache_key,
            data=content_data,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            file_hash=file_hash,
            file_size=file_size,
            expires_at=datetime.now() + timedelta(hours=self.ttl_hours)
        )
        
        await self.backend.set(cache_key, entry)
        logger.info(f"Cached content for: {file_path.name}")

    async def get_chunks_cache(self, file_path: Path) -> Optional[List]:
        """Get cached document chunks"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['chunks'], file_hash)

        entry = await self.backend.get(cache_key)
        if entry and entry.file_hash == file_hash:
            self.stats.hit_count += 1
            logger.info(f"Cache HIT for chunks: {file_path.name}")
            return entry.data

        self.stats.miss_count += 1
        logger.info(f"Cache MISS for chunks: {file_path.name}")
        return None

    async def set_chunks_cache(self, file_path: Path, chunks: List):
        """Cache document chunks"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['chunks'], file_hash)

        entry = CacheEntry(
            key=cache_key,
            data=chunks,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            file_hash=file_hash,
            file_size=file_size,
            expires_at=datetime.now() + timedelta(hours=self.ttl_hours)
        )

        await self.backend.set(cache_key, entry)
        logger.info(f"Cached chunks for: {file_path.name}")

    async def get_embeddings_cache(self, text_hash: str) -> Optional[np.ndarray]:
        """Get cached embeddings for text"""
        cache_key = self._make_cache_key(self.PREFIXES['embeddings'], text_hash)

        entry = await self.backend.get(cache_key)
        if entry:
            self.stats.hit_count += 1
            logger.debug(f"Cache HIT for embeddings: {text_hash[:8]}...")
            return entry.data

        self.stats.miss_count += 1
        logger.debug(f"Cache MISS for embeddings: {text_hash[:8]}...")
        return None

    async def set_embeddings_cache(self, text_hash: str, embeddings: np.ndarray):
        """Cache embeddings for text"""
        cache_key = self._make_cache_key(self.PREFIXES['embeddings'], text_hash)

        entry = CacheEntry(
            key=cache_key,
            data=embeddings,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=self.ttl_hours * 7)  # Embeddings last longer
        )

        await self.backend.set(cache_key, entry)
        logger.debug(f"Cached embeddings: {text_hash[:8]}...")

    async def get_processed_doc_cache(self, file_path: Path) -> Optional[Any]:
        """Get cached processed document"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['processed_doc'], file_hash)

        entry = await self.backend.get(cache_key)
        if entry and entry.file_hash == file_hash:
            self.stats.hit_count += 1
            logger.info(f"Cache HIT for processed doc: {file_path.name}")
            return entry.data

        self.stats.miss_count += 1
        logger.info(f"Cache MISS for processed doc: {file_path.name}")
        return None

    async def set_processed_doc_cache(self, file_path: Path, processed_doc: Any):
        """Cache processed document"""
        file_hash, file_size = self._generate_file_hash(file_path)
        cache_key = self._make_cache_key(self.PREFIXES['processed_doc'], file_hash)

        entry = CacheEntry(
            key=cache_key,
            data=processed_doc,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            file_hash=file_hash,
            file_size=file_size,
            expires_at=datetime.now() + timedelta(hours=self.ttl_hours)
        )

        await self.backend.set(cache_key, entry)
        logger.info(f"Cached processed doc for: {file_path.name}")

    def generate_text_hash(self, text: str) -> str:
        """Generate hash for text content"""
        return hashlib.sha256(text.encode()).hexdigest()

    async def invalidate_file_cache(self, file_path: Path):
        """Invalidate all cache entries for a file"""
        try:
            file_hash, _ = self._generate_file_hash(file_path)

            # Get all keys and find ones related to this file
            all_keys = await self.backend.keys()

            for key in all_keys:
                entry = await self.backend.get(key)
                if entry and entry.file_hash == file_hash:
                    await self.backend.delete(key)
                    logger.info(f"Invalidated cache entry: {key}")

        except Exception as e:
            logger.error(f"Error invalidating cache for {file_path}: {e}")

    async def cleanup_expired(self):
        """Remove expired cache entries"""
        try:
            all_keys = await self.backend.keys()
            expired_count = 0

            for key in all_keys:
                entry = await self.backend.get(key)
                if entry and entry.is_expired():
                    await self.backend.delete(key)
                    expired_count += 1

            if expired_count > 0:
                logger.info(f"Cleaned up {expired_count} expired cache entries")
                self.stats.eviction_count += expired_count

        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")

    async def enforce_size_limit(self):
        """Enforce cache size limits using LRU eviction"""
        try:
            current_size = await self.backend.size()
            max_size_bytes = self.max_size_mb * 1024 * 1024

            if current_size <= max_size_bytes:
                return

            logger.info(f"Cache size ({current_size / 1024 / 1024:.1f}MB) exceeds limit ({self.max_size_mb}MB), evicting entries...")

            # Get all entries with access info
            all_keys = await self.backend.keys()
            entries_info = []

            for key in all_keys:
                entry = await self.backend.get(key)
                if entry:
                    entries_info.append((key, entry.last_accessed, entry.access_count))

            # Sort by last accessed (LRU)
            entries_info.sort(key=lambda x: x[1])

            # Remove entries until under size limit
            evicted_count = 0
            for key, _, _ in entries_info:
                await self.backend.delete(key)
                evicted_count += 1

                current_size = await self.backend.size()
                if current_size <= max_size_bytes * 0.8:  # Leave some headroom
                    break

            logger.info(f"Evicted {evicted_count} cache entries to enforce size limit")
            self.stats.eviction_count += evicted_count

        except Exception as e:
            logger.error(f"Error enforcing cache size limit: {e}")

    async def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        try:
            all_keys = await self.backend.keys()
            cache_size = await self.backend.size()

            self.stats.total_entries = len(all_keys)
            self.stats.total_size_mb = cache_size / 1024 / 1024

            return self.stats

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return self.stats

    async def clear_all(self):
        """Clear all cache entries"""
        await self.backend.clear()
        self.stats = CacheStats()
        logger.info("Cleared all cache entries")

# Factory function for creating cache manager
def create_cache_manager(cache_type: str = "filesystem", **kwargs) -> DocumentCacheManager:
    """Create cache manager with specified backend"""

    if cache_type == "filesystem":
        cache_dir = kwargs.get('cache_dir', Path(config.cache_dir) if hasattr(config, 'cache_dir') else Path('.cache'))
        backend = FilesystemCacheBackend(cache_dir)

    elif cache_type == "redis":
        if not REDIS_AVAILABLE:
            logger.warning("Redis not available, falling back to filesystem cache")
            cache_dir = kwargs.get('cache_dir', Path(config.cache_dir) if hasattr(config, 'cache_dir') else Path('.cache'))
            backend = FilesystemCacheBackend(cache_dir)
        else:
            redis_url = kwargs.get('redis_url', 'redis://localhost:6379')
            db = kwargs.get('redis_db', 0)
            key_prefix = kwargs.get('key_prefix', 'docache:')
            backend = RedisCacheBackend(redis_url, db, key_prefix)

    else:
        raise ValueError(f"Unsupported cache type: {cache_type}")

    max_size_mb = kwargs.get('max_size_mb', 500)
    ttl_hours = kwargs.get('ttl_hours', 24)

    return DocumentCacheManager(backend, max_size_mb, ttl_hours)
