#!/usr/bin/env python3
"""
Startup script for the web interface
"""

import sys
import subprocess
import logging
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['fastapi', 'uvicorn', 'websockets']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them with:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main startup function"""
    print("🚀 Starting Technical Assistance Chatbot Web Interface...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check if .env file exists
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found!")
        print("Please run 'python setup.py' first to create the configuration file.")
        sys.exit(1)
    
    try:
        # Import configuration and run the web server
        from config import config
        import uvicorn

        host = config.web_host
        port = config.web_port
        workers = config.web_workers
        installation_id = config.installation_id

        print(f"✅ Starting web server for installation: {installation_id}")
        print(f"🌐 Server URL: http://{host}:{port}")
        print(f"📱 Open your browser and navigate to http://localhost:{port}")

        # Modalità sviluppo vs produzione
        is_development = "--dev" in sys.argv
        if is_development:
            print("🔧 Development mode: Auto-reload enabled (use --dev flag)")
        else:
            print("🚀 Production mode: Auto-reload disabled (add --dev for development)")

        print("🛑 Press Ctrl+C to stop the server")

        # Configurazione per sviluppo vs produzione
        is_development = "--dev" in sys.argv

        if is_development:
            # Modalità sviluppo con reload intelligente
            uvicorn.run(
                "web_server:app",
                host=host,
                port=port,
                workers=1,  # Solo 1 worker in dev mode
                reload=True,
                reload_excludes=[
                    "*.log",
                    "*.db",
                    "*.sqlite",
                    "*.cache",
                    "*cache*",
                    "*.json",
                    "__pycache__/*",
                    ".git/*",
                    "logs/*",
                    "cache/*",
                    "document_whitelist_cache.json"
                ],
                log_level="info"
            )
        else:
            # Modalità produzione senza reload
            uvicorn.run(
                "web_server:app",
                host=host,
                port=port,
                workers=workers,
                reload=False,
                log_level="info"
            )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
