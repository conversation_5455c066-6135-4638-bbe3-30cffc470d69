"""
Cache Service Integration
Provides high-level cache service integration for the document processing system
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional, List

from cache_manager import create_cache_manager, DocumentCacheManager
from cache_invalidator import CacheInvalidator, ScheduledCacheCleanup
from config import config

logger = logging.getLogger(__name__)

class CacheService:
    """High-level cache service for document processing"""
    
    def __init__(self, enable_monitoring: bool = True, enable_cleanup: bool = True):
        self.cache_manager: Optional[DocumentCacheManager] = None
        self.invalidator: Optional[CacheInvalidator] = None
        self.cleanup_service: Optional[ScheduledCacheCleanup] = None
        self.enable_monitoring = enable_monitoring
        self.enable_cleanup = enable_cleanup
        self.is_running = False
    
    async def initialize(self) -> bool:
        """Initialize cache service"""
        try:
            logger.info("Initializing cache service...")
            
            # Create cache manager
            self.cache_manager = create_cache_manager(
                cache_type=config.cache_type,
                cache_dir=config.cache_dir,
                max_size_mb=config.cache_max_size_mb,
                ttl_hours=config.cache_ttl_hours,
                redis_url=getattr(config, 'redis_url', 'redis://localhost:6379'),
                redis_db=getattr(config, 'redis_db', 0),
                key_prefix=getattr(config, 'redis_key_prefix', 'docache:')
            )
            
            logger.info(f"Cache manager initialized: {config.cache_type}")
            
            # Initialize file monitoring if enabled
            if self.enable_monitoring:
                watch_paths = [config.sorgenti_path]
                self.invalidator = CacheInvalidator(self.cache_manager, watch_paths)
                logger.info("Cache invalidator initialized")
            
            # Initialize cleanup service if enabled
            if self.enable_cleanup:
                cleanup_interval = getattr(config, 'cache_cleanup_interval_hours', 6)
                self.cleanup_service = ScheduledCacheCleanup(self.cache_manager, cleanup_interval)
                logger.info(f"Cache cleanup service initialized (interval: {cleanup_interval}h)")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize cache service: {e}")
            return False
    
    async def start(self):
        """Start cache service"""
        if self.is_running:
            logger.warning("Cache service is already running")
            return
        
        try:
            if not self.cache_manager:
                if not await self.initialize():
                    raise Exception("Failed to initialize cache service")
            
            # Start file monitoring
            if self.invalidator:
                self.invalidator.start_monitoring()
                logger.info("Cache file monitoring started")
            
            # Start cleanup service
            if self.cleanup_service:
                self.cleanup_service.start_cleanup()
                logger.info("Cache cleanup service started")
            
            self.is_running = True
            logger.info("Cache service started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start cache service: {e}")
            await self.stop()
    
    async def stop(self):
        """Stop cache service"""
        if not self.is_running:
            return
        
        try:
            # Stop file monitoring
            if self.invalidator:
                self.invalidator.stop_monitoring()
                logger.info("Cache file monitoring stopped")
            
            # Stop cleanup service
            if self.cleanup_service:
                self.cleanup_service.stop_cleanup()
                logger.info("Cache cleanup service stopped")
            
            # Close cache backend if needed
            if self.cache_manager and hasattr(self.cache_manager.backend, 'close'):
                await self.cache_manager.backend.close()
            
            self.is_running = False
            logger.info("Cache service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping cache service: {e}")
    
    def get_cache_manager(self) -> Optional[DocumentCacheManager]:
        """Get cache manager instance"""
        return self.cache_manager
    
    async def get_stats(self):
        """Get cache statistics"""
        if self.cache_manager:
            return await self.cache_manager.get_stats()
        return None
    
    async def manual_cleanup(self):
        """Manually trigger cache cleanup"""
        if self.cache_manager:
            await self.cache_manager.cleanup_expired()
            await self.cache_manager.enforce_size_limit()
            logger.info("Manual cache cleanup completed")
    
    async def invalidate_file(self, file_path: Path):
        """Manually invalidate cache for a file"""
        if self.cache_manager:
            await self.cache_manager.invalidate_file_cache(file_path)
            logger.info(f"Cache invalidated for: {file_path}")
    
    async def health_check(self) -> dict:
        """Perform cache service health check"""
        health = {
            'status': 'healthy',
            'cache_manager': bool(self.cache_manager),
            'monitoring': bool(self.invalidator and self.invalidator.is_running),
            'cleanup': bool(self.cleanup_service and self.cleanup_service.is_running),
            'stats': None,
            'errors': []
        }
        
        try:
            if self.cache_manager:
                health['stats'] = await self.cache_manager.get_stats()
        except Exception as e:
            health['errors'].append(f"Stats error: {e}")
            health['status'] = 'degraded'
        
        # Check cache backend connectivity
        try:
            if self.cache_manager:
                # Try a simple operation
                test_keys = await self.cache_manager.backend.keys()
                health['backend_accessible'] = True
        except Exception as e:
            health['errors'].append(f"Backend error: {e}")
            health['status'] = 'unhealthy'
            health['backend_accessible'] = False
        
        return health

# Global cache service instance
_cache_service: Optional[CacheService] = None

def get_cache_service() -> CacheService:
    """Get global cache service instance"""
    global _cache_service
    if _cache_service is None:
        _cache_service = CacheService()
    return _cache_service

async def initialize_cache_service(enable_monitoring: bool = True, enable_cleanup: bool = True) -> bool:
    """Initialize global cache service"""
    service = get_cache_service()
    service.enable_monitoring = enable_monitoring
    service.enable_cleanup = enable_cleanup
    return await service.initialize()

async def start_cache_service():
    """Start global cache service"""
    service = get_cache_service()
    await service.start()

async def stop_cache_service():
    """Stop global cache service"""
    service = get_cache_service()
    await service.stop()

class CacheMiddleware:
    """Middleware for automatic cache integration"""
    
    def __init__(self, cache_service: CacheService):
        self.cache_service = cache_service
    
    async def __aenter__(self):
        """Async context manager entry"""
        if not self.cache_service.is_running:
            await self.cache_service.start()
        return self.cache_service.get_cache_manager()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Don't stop the service on exit - let it run
        pass

def cache_middleware() -> CacheMiddleware:
    """Create cache middleware for automatic integration"""
    return CacheMiddleware(get_cache_service())

# Decorator for automatic cache integration
def with_cache(func):
    """Decorator to automatically provide cache manager to functions"""
    async def wrapper(*args, **kwargs):
        service = get_cache_service()
        if not service.is_running:
            await service.start()
        
        # Add cache_manager to kwargs if not present
        if 'cache_manager' not in kwargs:
            kwargs['cache_manager'] = service.get_cache_manager()
        
        return await func(*args, **kwargs)
    
    return wrapper

# Context manager for cache operations
class CacheContext:
    """Context manager for cache operations with automatic cleanup"""
    
    def __init__(self, auto_cleanup: bool = False):
        self.auto_cleanup = auto_cleanup
        self.cache_service = get_cache_service()
    
    async def __aenter__(self):
        if not self.cache_service.is_running:
            await self.cache_service.start()
        return self.cache_service.get_cache_manager()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.auto_cleanup:
            await self.cache_service.manual_cleanup()

def cache_context(auto_cleanup: bool = False) -> CacheContext:
    """Create cache context manager"""
    return CacheContext(auto_cleanup)

# Utility functions for common cache operations
async def warm_cache(file_paths: List[Path], progress_callback=None):
    """Warm cache by pre-processing files"""
    service = get_cache_service()
    cache_manager = service.get_cache_manager()
    
    if not cache_manager:
        logger.warning("Cache manager not available for cache warming")
        return
    
    from document_processor import DocumentProcessor
    processor = DocumentProcessor(enable_cache=True)
    
    total_files = len(file_paths)
    processed = 0
    
    for file_path in file_paths:
        try:
            if file_path.suffix.lower() == '.pdf':
                # Create mock resource
                from mcp_server import MCPResource
                resource = MCPResource(
                    name=f"warm/{file_path.name}",
                    uri=f"file://{file_path.absolute()}",
                    description="Cache warming",
                    metadata={
                        'file_path': str(file_path.absolute()),
                        'product': 'WARM',
                        'filename': file_path.name,
                        'citation_required': False
                    }
                )
                
                await processor.process_resource(resource)
                processed += 1
                
                if progress_callback:
                    progress_callback(processed, total_files, file_path)
                
                logger.info(f"Warmed cache for: {file_path.name} ({processed}/{total_files})")
        
        except Exception as e:
            logger.error(f"Error warming cache for {file_path}: {e}")
    
    logger.info(f"Cache warming completed: {processed}/{total_files} files processed")

async def get_cache_health() -> dict:
    """Get cache service health status"""
    service = get_cache_service()
    return await service.health_check()
