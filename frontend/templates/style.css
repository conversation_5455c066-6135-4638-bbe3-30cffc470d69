/* --- RESET E GLOBALI --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #F9F9F9;
    color: #050505;
    overflow: hidden; /* Impedisce lo scroll della pagina intera */
    /* Supporto per safe area su dispositivi con notch */
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
}

/* --- STRUTTURA PRINCIPALE --- */
.chat-container {
    display: flex;
    flex-direction: column;
    /* Usa dynamic viewport height per mobile */
    height: 100vh;
    height: 100dvh; /* Fallback moderno per browser che lo supportano */
    width: 100vw;
    background-color: #FFFFFF;
    position: relative;
}

/* --- HEADER --- */
.chat-header {
    position: relative; /* <PERSON><PERSON>ess<PERSON> per posizionare il menu contestuale */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #EAEAEA;
    flex-shrink: 0;
}

.chat-header .title {
    font-size: 18px;
    font-weight: 600;
}

.chat-header .options-icon {
    font-weight: bold;
    cursor: pointer;
    color: #8A8A8A;
    letter-spacing: 2px;
    padding: 5px;
}

/* --- MENU CONTESTUALE --- */
.context-menu {
    display: none; /* Nascosto di default */
    position: absolute;
    top: 50px; /* Posizionato sotto l'header */
    right: 24px;
    background-color: #FFFFFF;
    border: 1px solid #EAEAEA;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 10;
    width: 180px;
}

.context-menu.active {
    display: block; /* Visibile quando attivo */
}

.context-menu ul {
    list-style: none;
    padding: 8px 0;
}

.context-menu ul li a {
    display: block;
    padding: 10px 20px;
    text-decoration: none;
    color: #050505;
    font-size: 15px;
}

.context-menu ul li a:hover {
    background-color: #F0F0F0;
}


/* --- AREA MESSAGGI --- */
.chat-main {
    flex-grow: 1;
    overflow-y: auto;
    padding: 24px;
    background-color: #F9F9F9;
    display: flex;
    flex-direction: column;
    gap: 2px; /* Ridotto per avvicinare le bolle */
}

/* --- BOLLE DI CHAT --- */
.chat-bubble {
    max-width: 75%;
    padding: 12px 18px;
    border-radius: 20px; /* Raggio di default arrotondato */
    line-height: 1.5;
    font-size: 16px;
    margin-bottom: 10px; /* Spazio tra le bolle */
}

.chatbot {
    align-self: flex-start;
    background-color: #E9E9EB;
    color: #050505;
    /* La bolla del chatbot ha l'angolo in basso a sinistra appiattito, stile fumetto */
    border-bottom-left-radius: 5px;
}

.user {
    align-self: flex-end;
    background-color: #007AFF;
    color: #FFFFFF;
    /* La bolla dell'utente ha l'angolo in basso a destra appiattito, stile fumetto */
    border-bottom-right-radius: 5px;
}

/* --- FORMATTAZIONE MARKDOWN NELLE BOLLE --- */
.chat-bubble strong {
    font-weight: 600;
}

.chat-bubble em {
    font-style: italic;
}

.chat-bubble ul, .chat-bubble ol {
    margin: 8px 0;
    padding-left: 20px;
}

.chat-bubble li {
    margin: 4px 0;
    line-height: 1.4;
}

.chat-bubble ul li {
    list-style-type: disc;
}

.chat-bubble ol li {
    list-style-type: decimal;
}

/* --- INDICATORE "STA SCRIVENDO" --- */
.typing-indicator {
    align-self: flex-start;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 12px 18px;
    background-color: #E9E9EB;
    border-radius: 20px;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background-color: #8A8A8A;
    border-radius: 50%;
    animation: pulse 1.2s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

/* --- INDICATORE PENSIERO AVANZATO --- */
.thinking-indicator {
    background: linear-gradient(135deg, #E9E9EB 0%, #F5F5F7 100%);
    border-left: 3px solid #007AFF;
    animation: thinkingPulse 2s ease-in-out infinite;
}

.thinking-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.thinking-dots {
    display: flex;
    gap: 4px;
}

.thinking-dots span {
    height: 6px;
    width: 6px;
    background-color: #007AFF;
    border-radius: 50%;
    animation: thinkingDots 1.4s ease-in-out infinite;
}

.thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.thinking-text {
    font-size: 14px;
    color: #007AFF;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(0.9);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes thinkingDots {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes thinkingPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(0, 122, 255, 0.05);
    }
}

/* --- FOOTER INPUT --- */
.chat-footer {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    background-color: #FFFFFF;
    border-top: 1px solid #EAEAEA;
    flex-shrink: 0;
    /* Posizionamento fisso per mobile */
    position: sticky;
    bottom: 0;
    z-index: 100;
    /* Supporto per safe area bottom */
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.chat-footer .text-input {
    flex-grow: 1;
    border: none;
    outline: none;
    background-color: transparent;
    font-size: 16px;
    font-family: inherit;
}

.chat-footer .text-input::placeholder {
    color: #8A8A8A;
}

.chat-footer .send-button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-footer .send-button svg {
    width: 24px;
    height: 24px;
    fill: #8A8A8A;
    transition: fill 0.2s ease;
}

.chat-footer .send-button:hover svg {
    fill: #007AFF;
}

.chat-footer .send-button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.chat-footer .send-button:disabled svg {
    fill: #CCCCCC;
}

.chat-footer .text-input:disabled {
    color: #CCCCCC;
    cursor: not-allowed;
}

/* --- MEDIA QUERIES PER RESPONSIVENESS --- */
@media (max-width: 600px) {
    .chat-header {
        padding: 12px 16px;
        /* Supporto per safe area top */
        padding-top: calc(12px + env(safe-area-inset-top));
    }

    .chat-footer {
        padding: 12px 16px;
        /* Supporto per safe area bottom */
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        /* Assicura che rimanga visibile anche con la tastiera */
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .chat-main {
        padding: 16px;
        /* Lascia spazio per il footer fisso */
        padding-bottom: calc(80px + env(safe-area-inset-bottom));
    }

    .chat-bubble {
        max-width: 85%;
    }

    /* Gestione della tastiera virtuale su iOS */
    .chat-container {
        height: 100vh;
        height: 100dvh; /* Dynamic viewport height */
    }
}

/* --- SUPPORTO PER DISPOSITIVI CON TASTIERA VIRTUALE --- */
@supports (height: 100dvh) {
    .chat-container {
        height: 100dvh;
    }
}

/* --- MEDIA QUERY PER DISPOSITIVI MOLTO PICCOLI --- */
@media (max-width: 400px) {
    .chat-footer {
        padding: 8px 12px;
        padding-bottom: calc(8px + env(safe-area-inset-bottom));
    }

    .chat-header {
        padding: 8px 12px;
        padding-top: calc(8px + env(safe-area-inset-top));
    }

    .chat-main {
        padding: 12px;
        padding-bottom: calc(70px + env(safe-area-inset-bottom));
    }
}

/* --- GESTIONE TASTIERA VIRTUALE --- */
body.keyboard-visible .chat-main {
    padding-bottom: calc(120px + env(safe-area-inset-bottom));
}

body.keyboard-visible .chat-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1001;
}

/* --- PREVENZIONE ZOOM SU IOS --- */
@media screen and (max-width: 600px) {
    .text-input {
        font-size: 16px !important; /* Previene lo zoom automatico su iOS */
        transform: translateZ(0); /* Forza l'accelerazione hardware */
    }
}

/* --- MIGLIORAMENTI PER TOUCH --- */
.send-button {
    min-height: 44px; /* Dimensione minima per touch target */
    min-width: 44px;
}

.text-input {
    min-height: 44px; /* Dimensione minima per touch target */
}

/* --- Stili per la modale delle fonti --- */
.sources-icon {
    margin-top: 10px;
    cursor: pointer;
    color: #8A8A8A;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(138, 138, 138, 0.1);
    font-size: 12px;
    user-select: none;
}

.sources-icon:hover {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
    transform: translateY(-1px);
}

.sources-count {
    font-weight: 600;
    font-size: 11px;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6); /* Sfondo più scuro */
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
}

.modal-content {
    background: #FFFFFF;
    padding: 32px;
    border-radius: 12px; /* Bordi più arrotondati */
    width: 90%;
    max-width: 680px; /* Larghezza massima */
    height: 85%;
    max-height: 600px; /* Altezza massima */
    overflow-y: auto;
    position: relative;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-content h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px; /* Aumentato spazio sotto */
    padding-bottom: 12px; /* Aggiunto padding sotto */
    border-bottom: 1px solid #EAEAEA; /* Separatore */
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 36px; /* Più grande */
    font-weight: 300;
    color: #8A8A8A;
    cursor: pointer;
    border: none;
    background: none;
    line-height: 1;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #050505;
}

.sources-list {
    list-style: none;
    padding-top: 16px; /* Spazio sopra la lista */
}

.sources-list li {
    margin-bottom: 0; /* Rimosso margine per far spazio al padding */
}

.sources-list a {
    display: block;
    padding: 14px 18px;
    text-decoration: none;
    color: #050505;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 16px;
    position: relative;
}

.sources-list a:hover {
    background-color: #F0F8FF;
    color: #007AFF;
    text-decoration: underline;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.sources-list a:active {
    transform: translateX(2px);
}

.sources-list a .page-number {
    font-weight: 600;
    color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 8px;
}

/* Icona per indicare link esterno */
.sources-list a[target="_blank"]::after {
    content: "↗";
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #8A8A8A;
    transition: color 0.2s ease;
}

.sources-list a[target="_blank"]:hover::after {
    color: #007AFF;
}

/* --- PRODUCT SELECTION BUTTONS --- */
.product-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.product-button {
    padding: 8px 16px;
    border: 1px solid #007AFF;
    background: white;
    color: #007AFF;
    border-radius: 16px;
    cursor: pointer;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
}

.product-button:hover {
    background-color: #007AFF;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.product-button:active {
    transform: translateY(0);
}

/* --- LOADING AND ERROR STATES --- */
.loading-message {
    font-style: italic;
    color: #8A8A8A;
}

.error-message {
    color: #FF3B30;
    background-color: #FFE5E5;
    border-left: 3px solid #FF3B30;
    padding: 12px;
    border-radius: 8px;
    margin: 10px 0;
}

/* --- RESPONSIVE IMPROVEMENTS --- */
@media (max-width: 600px) {
    .product-buttons {
        flex-direction: column;
    }

    .product-button {
        width: 100%;
        text-align: center;
    }
}