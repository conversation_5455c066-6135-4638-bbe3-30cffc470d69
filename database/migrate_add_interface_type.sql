-- =====================================================
-- Migration Script: Add interface_type column
-- Adds interface_type column to existing chatbot_logs table
-- =====================================================

USE softway_chat;

-- Add the interface_type column
ALTER TABLE chatbot_logs 
ADD COLUMN interface_type ENUM('CLI', 'WEB') NOT NULL DEFAULT 'CLI' 
COMMENT 'Tipo di interfaccia (CLI o WEB)' 
AFTER product;

-- Create index for the new column
CREATE INDEX idx_interface_type ON chatbot_logs(interface_type);

-- Update existing records based on IP address heuristics
-- Records with localhost IPs are likely CLI, others are likely WEB
UPDATE chatbot_logs 
SET interface_type = CASE 
    WHEN ip_address IN ('127.0.0.1', '::1', 'localhost') OR ip_address IS NULL THEN 'CLI'
    ELSE 'WEB'
END;

-- Show migration results
SELECT 
    interface_type,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM chatbot_logs 
GROUP BY interface_type;

SELECT 'Migration completed successfully' as status;
