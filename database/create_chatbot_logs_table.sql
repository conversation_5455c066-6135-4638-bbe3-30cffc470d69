-- =====================================================
-- Schema Database per Log Chatbot
-- Database: softway_chat
-- Tabella: chatbot_logs
-- =====================================================

-- Usa il database specificato nel file .env
USE softway_chat;

-- Crea la tabella per i log delle conversazioni del chatbot
CREATE TABLE IF NOT EXISTS chatbot_logs (
    -- Chiave primaria auto-incrementale
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Timestamp di creazione del record
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indirizzo IP dell'utente (supporta IPv4 e IPv6)
    ip_address VARCHAR(45) NULL COMMENT 'Indirizzo IP utente (IPv4/IPv6)',
    
    -- Identificatore univoco della sessione di conversazione
    session_id VARCHAR(255) NOT NULL COMMENT 'ID sessione conversazione',
    
    -- Linea di prodotto per filtraggio
    product VARCHAR(100) NULL COMMENT 'Linea di prodotto',

    -- Tipo di interfaccia utilizzata
    interface_type ENUM('CLI', 'WEB') NOT NULL DEFAULT 'CLI' COMMENT 'Tipo di interfaccia (CLI o WEB)',

    -- Domanda dell'utente
    question TEXT NOT NULL COMMENT 'Testo completo della domanda utente',
    
    -- Risposta del chatbot (può essere molto lunga)
    answer MEDIUMTEXT NULL COMMENT 'Testo della risposta del chatbot',
    
    -- Numero di documenti/risultati utilizzati nella ricerca
    search_result_count INT DEFAULT 0 COMMENT 'Numero di documenti utilizzati',
    
    -- Punteggio medio di pertinenza dei risultati
    avg_relevance_score DECIMAL(5, 4) NULL COMMENT 'Punteggio medio pertinenza (0.0000-1.0000)',
    
    -- Flag per indicare se è stata utilizzata conoscenza generale
    used_general_knowledge BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Uso conoscenza generale',
    
    -- Tempo di risposta in millisecondi
    response_time_ms INT NULL COMMENT 'Tempo di risposta in ms',
    
    -- Punteggio di confidenza della risposta
    confidence DECIMAL(5, 4) NULL COMMENT 'Punteggio confidenza risposta (0.0000-1.0000)',
    
    -- Log strutturato degli strumenti eseguiti (formato JSON)
    tool_executions JSON NULL COMMENT 'Log strutturato strumenti eseguiti'
    
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Tabella per il logging delle conversazioni del chatbot';

-- =====================================================
-- INDICI PER OTTIMIZZAZIONE QUERY
-- =====================================================

-- Indice su session_id per query per sessione
CREATE INDEX idx_session_id ON chatbot_logs(session_id);

-- Indice su created_at per query temporali
CREATE INDEX idx_created_at ON chatbot_logs(created_at);

-- Indice su product per filtraggio per linea di prodotto
CREATE INDEX idx_product ON chatbot_logs(product);

-- Indice composto per query comuni (sessione + tempo)
CREATE INDEX idx_session_time ON chatbot_logs(session_id, created_at);

-- Indice per query di performance (tempo di risposta)
CREATE INDEX idx_response_time ON chatbot_logs(response_time_ms);

-- Indice per tipo di interfaccia
CREATE INDEX idx_interface_type ON chatbot_logs(interface_type);

-- =====================================================
-- COMMENTI E DOCUMENTAZIONE
-- =====================================================

/*
SPECIFICHE TECNICHE:
- Motore: InnoDB per integrità referenziale e transazioni
- Character Set: utf8mb4 per compatibilità Unicode completa
- Collation: utf8mb4_unicode_ci per ordinamento corretto

CAMPI PRINCIPALI:
- id: Chiave primaria auto-incrementale
- created_at: Timestamp automatico di creazione
- session_id: Identifica una conversazione completa
- question/answer: Contenuto della conversazione
- Metriche: search_result_count, avg_relevance_score, confidence
- Performance: response_time_ms
- Metadata: ip_address, product, used_general_knowledge
- Logging: tool_executions (JSON per flessibilità)

INDICI OTTIMIZZATI PER:
- Query per sessione (session_id)
- Query temporali (created_at)
- Filtraggio per prodotto (product)
- Analisi performance (response_time_ms)
- Query combinate sessione+tempo

ESEMPI DI QUERY OTTIMIZZATE:
- SELECT * FROM chatbot_logs WHERE session_id = 'xxx'
- SELECT * FROM chatbot_logs WHERE created_at >= '2024-01-01'
- SELECT * FROM chatbot_logs WHERE product = 'ProductA'
- SELECT AVG(response_time_ms) FROM chatbot_logs WHERE created_at >= NOW() - INTERVAL 1 DAY
*/
