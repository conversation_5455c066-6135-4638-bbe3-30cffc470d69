-- =====================================================
-- Script di Test per Tabella chatbot_logs
-- Database: softway_chat
-- =====================================================

USE softway_chat;

-- =====================================================
-- INSERIMENTO DATI DI TEST
-- =====================================================

-- Test 1: Conversazione base con ricerca documenti (WEB)
INSERT INTO chatbot_logs (
    ip_address,
    session_id,
    product,
    interface_type,
    question,
    answer,
    search_result_count,
    avg_relevance_score,
    used_general_knowledge,
    response_time_ms,
    confidence,
    tool_executions
) VALUES (
    '*************',
    'session_001_2024',
    'ProductA',
    'WEB',
    'Come posso configurare il sistema di backup automatico?',
    'Per configurare il backup automatico, segui questi passaggi: 1) Accedi al pannello di controllo...',
    5,
    0.8750,
    FALSE,
    1250,
    0.9200,
    JSON_OBJECT(
        'tools_used', JSON_ARRAY('document_search', 'knowledge_retrieval'),
        'search_queries', JSON_ARRAY('backup automatico', 'configurazione sistema'),
        'execution_time_ms', JSON_OBJECT('document_search', 800, 'knowledge_retrieval', 450)
    )
);

-- Test 2: Conversazione con conoscenza generale (CLI)
INSERT INTO chatbot_logs (
    ip_address,
    session_id,
    product,
    interface_type,
    question,
    answer,
    search_result_count,
    avg_relevance_score,
    used_general_knowledge,
    response_time_ms,
    confidence,
    tool_executions
) VALUES (
    '127.0.0.1',
    'session_002_2024',
    'ProductB',
    'CLI',
    'Che cos\'è l\'intelligenza artificiale?',
    'L\'intelligenza artificiale (IA) è una branca dell\'informatica che si occupa di creare sistemi...',
    0,
    NULL,
    TRUE,
    850,
    0.7500,
    JSON_OBJECT(
        'tools_used', JSON_ARRAY('general_knowledge'),
        'fallback_reason', 'no_relevant_documents_found',
        'execution_time_ms', JSON_OBJECT('general_knowledge', 850)
    )
);

-- Test 3: Conversazione con IPv6 e alta confidenza (WEB)
INSERT INTO chatbot_logs (
    ip_address,
    session_id,
    product,
    interface_type,
    question,
    answer,
    search_result_count,
    avg_relevance_score,
    used_general_knowledge,
    response_time_ms,
    confidence,
    tool_executions
) VALUES (
    '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
    'session_003_2024',
    'ProductA',
    'WEB',
    'Quali sono i requisiti di sistema minimi?',
    'I requisiti minimi di sistema sono: CPU: Intel i5 o equivalente, RAM: 8GB, Storage: 50GB...',
    3,
    0.9500,
    FALSE,
    650,
    0.9800,
    JSON_OBJECT(
        'tools_used', JSON_ARRAY('document_search', 'specification_lookup'),
        'search_queries', JSON_ARRAY('requisiti sistema', 'specifiche tecniche'),
        'execution_time_ms', JSON_OBJECT('document_search', 400, 'specification_lookup', 250)
    )
);

-- =====================================================
-- QUERY DI TEST E VERIFICA
-- =====================================================

-- Verifica inserimento dati
SELECT 'Totale record inseriti:' as info, COUNT(*) as count FROM chatbot_logs;

-- Test query per sessione
SELECT 
    session_id,
    question,
    response_time_ms,
    confidence
FROM chatbot_logs 
WHERE session_id = 'session_001_2024';

-- Test query per prodotto
SELECT 
    product,
    COUNT(*) as conversations,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence
FROM chatbot_logs 
WHERE product IS NOT NULL
GROUP BY product;

-- Test query temporali (ultime 24 ore)
SELECT 
    DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour_bucket,
    COUNT(*) as conversations,
    AVG(response_time_ms) as avg_response_time
FROM chatbot_logs 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY hour_bucket
ORDER BY hour_bucket;

-- Test query performance (conversazioni lente)
SELECT 
    session_id,
    question,
    response_time_ms,
    confidence,
    created_at
FROM chatbot_logs 
WHERE response_time_ms > 1000
ORDER BY response_time_ms DESC;

-- Test query JSON (strumenti utilizzati)
SELECT 
    session_id,
    question,
    JSON_EXTRACT(tool_executions, '$.tools_used') as tools_used,
    JSON_EXTRACT(tool_executions, '$.execution_time_ms') as execution_times
FROM chatbot_logs 
WHERE tool_executions IS NOT NULL;

-- Test query per tipo di interfaccia
SELECT
    interface_type,
    COUNT(*) as conversations,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence
FROM chatbot_logs
GROUP BY interface_type;

-- Statistiche generali
SELECT
    'Statistiche Generali' as report,
    COUNT(*) as total_conversations,
    AVG(response_time_ms) as avg_response_time,
    AVG(confidence) as avg_confidence,
    AVG(search_result_count) as avg_search_results,
    SUM(CASE WHEN used_general_knowledge = TRUE THEN 1 ELSE 0 END) as general_knowledge_usage,
    SUM(CASE WHEN interface_type = 'CLI' THEN 1 ELSE 0 END) as cli_conversations,
    SUM(CASE WHEN interface_type = 'WEB' THEN 1 ELSE 0 END) as web_conversations
FROM chatbot_logs;
